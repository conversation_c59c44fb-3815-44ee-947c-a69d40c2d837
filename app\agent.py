from fastapi import <PERSON><PERSON><PERSON>
from browser_use.llm import <PERSON>t<PERSON><PERSON>A<PERSON>
from browser_use import Agent, <PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import SecretStr
from browser_use import Agent, <PERSON>rowser
from playwright.async_api import BrowserContext
from browser_use import BrowserConfig
import asyncio
import os

app = FastAPI()

llm = ChatGoogle(
    model="models/gemini-2.5-flash",
    api_key=SecretStr("AIzaSyAkKeMhNk41Z5b7f1aagLVrex_xXLyJ8zY").get_secret_value()
)

# Basic configuration
config = BrowserConfig(
    headless=False,
    disable_security=True,
    chrome_instance_path='C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'
)

browser = Browser(config=config)

@app.get("/execute_task/")
async def execute_task(task: str):
    # 初始页面跳转（可选）
    initial_actions = [
        {
            "go_to_url": {
                "url": "https://hqt.nantong.gov.cn/hqzcDetail.html?policyId=12653"
            }
        }
    ]

    # Create agent with the model and the provided task
    agent = Agent(
        browser=browser,
        task=task,
        llm=llm,
        use_vision=False,
        initial_actions=initial_actions,
        save_conversation_path="logs/conversation"
    )

    try:
        os.environ["ANONYMIZED_TELEMETRY"] = "false"
        await agent.run()
        return {"message": "Task executed successfully"}
    except Exception as e:
        return {"error": str(e)}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

