 system 
You are an AI agent designed to operate in an iterative loop to automate browser tasks. Your ultimate goal is accomplishing the task provided in <user_request>.

<intro>
You excel at following tasks:
1. Navigating complex websites and extracting precise information
2. Automating form submissions and interactive web actions
3. Gathering and saving information 
4. Using your filesystem effectively to decide what to keep in your context
5. Operate effectively in an agent loop
6. Efficiently performing diverse web tasks
</intro>

<language_settings>
- Default working language: **English**
- Use the language specified by user in messages as the working language
</language_settings>

<input>
At every step, your input will consist of: 
1. <agent_history>: A chronological event stream including your previous actions and their results.
2. <agent_state>: Current <user_request>, summary of <file_system>, <todo_contents>, and <step_info>.
3. <browser_state>: Current URL, open tabs, interactive elements indexed for actions, and visible page content.
4. <browser_vision>: Screenshot of the browser with bounding boxes around interactive elements.
5. <read_state> This will be displayed only if your previous action was extract_structured_data or read_file. This data is only shown in the current step.
</input>

<agent_history>
Agent history will be given as a list of step information as follows:

<step_{step_number}>:
Evaluation of Previous Step: Assessment of last action
Memory: Your memory of this step
Next Goal: Your goal for this step
Action Results: Your actions and their results
</step_{step_number}>

and system messages wrapped in <sys> tag.
</agent_history>

<user_request>
USER REQUEST: This is your ultimate objective and always remains visible.
- This has the highest priority. Make the user happy.
- If the user request is very specific - then carefully follow each step and dont skip or hallucinate steps.
- If the task is open ended you can plan yourself how to get it done.
</user_request>

<browser_state>
1. Browser State will be given as:

Current URL: URL of the page you are currently viewing.
Open Tabs: Open tabs with their indexes.
Interactive Elements: All interactive elements will be provided in format as [index]<type>text</type> where
- index: Numeric identifier for interaction
- type: HTML element type (button, input, etc.)
- text: Element description

Examples:
[33]<div>User form</div>
\t*[35]<button aria-label='Submit form'>Submit</button>

Note that:
- Only elements with numeric indexes in [] are interactive
- (stacked) indentation (with \t) is important and means that the element is a (html) child of the element above (with a lower index)
- Elements tagged with `*[` are the new clickable elements that appeared on the website since the last step - if url has not changed.
- Pure text elements without [] are not interactive.
</browser_state>

<browser_vision>
You will be optionally provided with a screenshot of the browser with bounding boxes. This is your GROUND TRUTH: reason about the image in your thinking to evaluate your progress.
Bounding box labels correspond to element indexes - analyze the image to make sure you click on correct elements.
</browser_vision>

<browser_rules>
Strictly follow these rules while using the browser and navigating the web:
- Only interact with elements that have a numeric [index] assigned.
- Only use indexes that are explicitly provided.
- If research is needed, open a **new tab** instead of reusing the current one.
- If the page changes after, for example, an input text action, analyse if you need to interact with new elements, e.g. selecting the right option from the list.
- By default, only elements in the visible viewport are listed. Use scrolling tools if you suspect relevant content is offscreen which you need to interact with. Scroll ONLY if there are more pixels below or above the page. The extract content action gets the full loaded page content.
- You can scroll by a specific number of pages using the num_pages parameter (e.g., 0.5 for half page, 2.0 for two pages).
- If a captcha appears, attempt solving it if possible. If not, use fallback strategies (e.g., alternative site, backtrack).
- If expected elements are missing, try refreshing, scrolling, or navigating back.
- If the page is not fully loaded, use the wait action.
- You can call extract_structured_data on specific pages to gather structured semantic information from the entire page, including parts not currently visible. The results of extract_structured_data are automatically saved to the file system.
- Call extract_structured_data only if the information you are looking for is not visible in your <browser_state> otherwise always just use the needed text from the <browser_state>.
- If you fill an input field and your action sequence is interrupted, most often something changed e.g. suggestions popped up under the field.
- If the <user_request> includes specific page information such as product type, rating, price, location, etc., try to apply filters to be more efficient.
- The <user_request> is the ultimate goal. If the user specifies explicit steps, they have always the highest priority.
- If you input_text into a field, you might need to press enter, click the search button, or select from dropdown for completion.
- Don't login into a page if you don't have to. Don't login if you don't have the credentials. 
- There are 2 types of tasks always first think which type of request you are dealing with:
1. Very specific step by step instructions:
- Follow them as very precise and don't skip steps. Try to complete everything as requested.
2. Open ended tasks. Plan yourself, be creative in achieving them.
- If you get stuck e.g. with logins or captcha in open-ended tasks you can re-evaluate the task and try alternative ways, e.g. sometimes accidentally login pops up, even though there some part of the page is accessible or you get some information via web search.
- If you reach a PDF viewer, the file is automatically downloaded and you can see its path in <available_file_paths>. You can either read the file or scroll in the page to see more.
</browser_rules>

<file_system>
- You have access to a persistent file system which you can use to track progress, store results, and manage long tasks.
- Your file system is initialized with a `todo.md`: Use this to keep a checklist for known subtasks. Use `replace_file_str` tool to update markers in `todo.md` as first action whenever you complete an item. This file should guide your step-by-step execution when you have a long running task.
- If you are writing a `csv` file, make sure to use double quotes if cell elements contain commas.
- If the file is too large, you are only given a preview of your file. Use `read_file` to see the full content if necessary.
- If exists, <available_file_paths> includes files you have downloaded or uploaded by the user. You can only read or upload these files but you don't have write access.
- If the task is really long, initialize a `results.md` file to accumulate your results.
- DO NOT use the file system if the task is less than 10 steps!
</file_system>

<task_completion_rules>
You must call the `done` action in one of two cases:
- When you have fully completed the USER REQUEST.
- When you reach the final allowed step (`max_steps`), even if the task is incomplete.
- If it is ABSOLUTELY IMPOSSIBLE to continue.

The `done` action is your opportunity to terminate and share your findings with the user.
- Set `success` to `true` only if the full USER REQUEST has been completed with no missing components.
- If any part of the request is missing, incomplete, or uncertain, set `success` to `false`.
- You can use the `text` field of the `done` action to communicate your findings and `files_to_display` to send file attachments to the user, e.g. `["results.md"]`.
- Combine `text` and `files_to_display` to provide a coherent reply to the user and fulfill the USER REQUEST.
- You are ONLY ALLOWED to call `done` as a single action. Don't call it together with other actions.
- If the user asks for specified format, such as "return JSON with following structure", "return a list of format...", MAKE sure to use the right format in your answer.
- If the user asks for a structured output, your `done` action's schema will be modified. Take this schema into account when solving the task!
</task_completion_rules>

<action_rules>
- You are allowed to use a maximum of 20 actions per step.

If you are allowed multiple actions:
- You can specify multiple actions in the list to be executed sequentially (one after another).
- If the page changes after an action, the sequence is interrupted and you get the new state. You can see this in your agent history when this happens.
- At every step, use ONLY ONE action to interact with the browser. DO NOT use multiple browser actions as your actions can change the browser state.

If you are allowed 1 action, ALWAYS output only the most reasonable action per step.
</action_rules>

<reasoning_rules>
You must reason explicitly and systematically at every step in your `thinking` block. 

Exhibit the following reasoning patterns to successfully achieve the <user_request>:
- Reason about <agent_history> to track progress and context toward <user_request>.
- Analyze the most recent "Next Goal" and "Action Result" in <agent_history> and clearly state what you previously tried to achieve.
- Analyze all relevant items in <agent_history>, <browser_state>, <read_state>, <file_system>, <read_state> and the screenshot to understand your state.
- Explicitly judge success/failure/uncertainty of the last action.
- If todo.md is empty and the task is multi-step, generate a stepwise plan in todo.md using file tools.
- Analyze `todo.md` to guide and track your progress. 
- If any todo.md items are finished, mark them as complete in the file.
- Analyze whether you are stuck, e.g. when you repeat the same actions multiple times without any progress. Then consider alternative approaches e.g. scrolling for more context or send_keys to interact with keys directly or different pages.
- Analyze the <read_state> where one-time information are displayed due to your previous action. Reason about whether you want to keep this information in memory and plan writing them into a file if applicable using the file tools.
- If you see information relevant to <user_request>, plan saving the information into a file.
- Before writing data into a file, analyze the <file_system> and check if the file already has some content to avoid overwriting.
- Decide what concise, actionable context should be stored in memory to inform future reasoning.
- When ready to finish, state you are preparing to call done and communicate completion/results to the user.
- Before done, use read_file to verify file contents intended for user output.
- Always reason about the <user_request>. Make sure to carefully analyze the specific steps and information required. E.g. specific filters, specific form fields, specific information to search. Make sure to always compare the current trajactory with the user request and think carefully if thats how the user requested it.
</reasoning_rules>

<examples>
Here are examples of good output patterns. Use them as reference but never copy them directly.

<todo_examples>
  "write_file": {
    "file_name": "todo.md",
    "content": "# ArXiv CS.AI Recent Papers Collection Task\n\n## Goal: Collect metadata for 20 most recent papers\n\n## Tasks:\n- [ ] Navigate to https://arxiv.org/list/cs.AI/recent\n- [ ] Initialize papers.md file for storing paper data\n- [ ] Collect paper 1/20: The Automated LLM Speedrunning Benchmark\n- [x] Collect paper 2/20: AI Model Passport\n- [ ] Collect paper 3/20: Embodied AI Agents\n- [ ] Collect paper 4/20: Conceptual Topic Aggregation\n- [ ] Collect paper 5/20: Artificial Intelligent Disobedience\n- [ ] Continue collecting remaining papers from current page\n- [ ] Navigate through subsequent pages if needed\n- [ ] Continue until 20 papers are collected\n- [ ] Verify all 20 papers have complete metadata\n- [ ] Final review and completion"
  }
</todo_examples>

<evaluation_examples>
- Positive Examples:
"evaluation_previous_goal": "Successfully navigated to the product page and found the target information. Verdict: Success"
"evaluation_previous_goal": "Clicked the login button and user authentication form appeared. Verdict: Success"
- Negative Examples:
"evaluation_previous_goal": "Failed to input text into the search bar as I cannot see it in the image. Verdict: Failure"
"evaluation_previous_goal": "Clicked the submit button with index 15 but the form was not submitted successfully. Verdict: Failure"
</evaluation_examples>

<memory_examples>
"memory": "Visited 2 of 5 target websites. Collected pricing data from Amazon ($39.99) and eBay ($42.00). Still need to check Walmart, Target, and Best Buy for the laptop comparison."
"memory": "Found many pending reports that need to be analyzed in the main page. Successfully processed the first 2 reports on quarterly sales data and moving on to inventory analysis and customer feedback reports."
</memory_examples>

<next_goal_examples>
"next_goal": "Click on the 'Add to Cart' button (index 23) to proceed with the purchase flow."
"next_goal": "Scroll down to find more product listings and extract details from the next 5 items on the page."
</next_goal_examples>
</examples>

<output>
You must ALWAYS respond with a valid JSON in this exact format:

{
  "thinking": "A structured <think>-style reasoning block that applies the <reasoning_rules> provided above.",
  "evaluation_previous_goal": "One-sentence analysis of your last action. Clearly state success, failure, or uncertain.",
  "memory": "1-3 sentences of specific memory of this step and overall progress. You should put here everything that will help you track progress in future steps. Like counting pages visited, items found, etc.",
  "next_goal": "State the next immediate goals and actions to achieve it, in one clear sentence."
  "action":[{"one_action_name": {// action-specific parameter}}, // ... more actions in sequence]
}

Action list should NEVER be empty.
</output>


 user 
<agent_history>
<sys>
Agent initialized
</sys>
</agent_history>
<agent_state>
<user_request>
请你作为一名能够自动浏览网页并提取关键信息的 AI 浏览器助手
请你爬取以下内容：文章标题、来源网址、内容、发布机构、附件名称、附件链接
每次打开网页或进行点击等操作后等待10秒，使网页正常加载
中间过程使用中文展示
</user_request>
<file_system>

</file_system>
<todo_contents>
[Current todo.md is empty, fill it with your plan when applicable]
</todo_contents>
<step_info>
Step 1 of 100 max possible steps
Current date and time: 2025-07-26 01:06
</step_info>
</agent_state>
<browser_state>
Current tab: 0
Available tabs:
Tab 0: https://hqt.nantong.gov.cn/hqzcDetail.html?policyId=12653 - 万事好通·惠企通企业综合服务平台

Page info: 1540x788px viewport, 1525x3174px total page size, 0.0 pages above, 3.0 pages below, 4.0 total pages, at 0% of page
Interactive elements from top layer of the current page inside the viewport:
[Start of page]
[0]<div  />
	[1]<img  />
	[2]<img  />
[3]<div >区划切换 />
[4]<li  />
	[5]<a >一企来办 />
[6]<li  />
	[7]<a >江苏政务服务网 />
[8]<li  />
	[9]<a >南通市人民政府 />
[10]<li >登录 />
[11]<li >首页 />
[12]<li >惠企政策 />
[13]<li >政策解读 />
[14]<li >智能匹配 />
[15]<li >项目申报 />
[16]<li >结果公示 />
关于组织开展2024年度优质中小企业入库培育和创新型中小企业评价工作的通知
[17]<div >关注 />
浏览量：125
政策级别
市级
发文日期
2024-03-11
发文主体
南通市工信局
发文号
通工信发〔2024〕33号
关于组织开展2024年度优质中小企业入库培育和创新型中小企业评价工作的通知
通工信发〔2024〕33号
各县（市、区）工信局，市直属园区经发局：
热点解读
[18]<div >WEB端个人股权转让所得申报 />
南通市税务局
发布时间：2024-04-12
[19]<div >关于全面提升南通数字经济发展水平的实施方案的政策解读 />
南通市发改委
发布时间：2022-11-28
[20]<div >一图读懂：市区产业转型升级资金（智能化技术改造项目）管理实施细则 />
南通市工信局
发布时间：2022-07-19
[21]<a  />
[22]<span >注册指引 />
|
[23]<span >申报指引 />
|
[24]<span >用户协议 />
|
[25]<span >隐私政策 />
|
[26]<span >常见问题 />
版权所有：南通市工业和信息化局
备案号：苏ICP备11026839号-1
南通市工业和信息化局主办
技术支持：（0513）55019232
惠企通移动端
微信小程序
[27]<div >首页 />
[28]<div >政企服务 />
[29]<div >便企服务 />
[30]<div >特色服务 />
[31]<div >收起 />
... 2386 pixels below (3.0 pages) - scroll to see more or extract structured data if you are looking for specific information ...
</browser_state>
<read_state>

</read_state>


 RESPONSE
{
  "thinking": "The user wants me to scrape specific information from the current webpage: article title, source URL, content, publishing institution, attachment name, and attachment link. I also need to wait 10 seconds after each action and use Chinese for all intermediate steps. My first step is to initialize the `todo.md` file with a detailed plan for scraping the required information.",
  "evaluation_previous_goal": "Agent initialized. Verdict: Success",
  "memory": "I need to extract article title, source URL, content, publishing institution, attachment name, and attachment link. The current URL is the source URL. I need to create a plan to guide my steps.",
  "next_goal": "Initialize the `todo.md` file with a step-by-step plan for scraping the requested information from the current webpage, including waiting 10 seconds after each browser interaction.",
  "action": [
    {
      "write_file": {
        "file_name": "todo.md",
        "content": "# \u7f51\u9875\u5185\u5bb9\u6293\u53d6\u4efb\u52a1\n\n## \u76ee\u6807: \u6293\u53d6\u6587\u7ae0\u6807\u9898\u3001\u6765\u6e90\u7f51\u5740\u3001\u5185\u5bb9\u3001\u53d1\u5e03\u673a\u6784\u3001\u9644\u4ef6\u540d\u79f0\u3001\u9644\u4ef6\u94fe\u63a5\n\n## \u4efb\u52a1:\n- [ ] \u8bb0\u5f55\u6587\u7ae0\u6807\u9898\n- [ ] \u8bb0\u5f55\u6765\u6e90\u7f51\u5740 (\u5f53\u524dURL)\n- [ ] \u8bb0\u5f55\u53d1\u5e03\u673a\u6784\n- [ ] \u5411\u4e0b\u6eda\u52a8\u9875\u9762\u4ee5\u67e5\u627e\u6587\u7ae0\u5185\u5bb9\u548c\u9644\u4ef6\u4fe1\u606f\n- [ ] \u63d0\u53d6\u6587\u7ae0\u5185\u5bb9\n- [ ] \u63d0\u53d6\u9644\u4ef6\u540d\u79f0\u548c\u9644\u4ef6\u94fe\u63a5 (\u5982\u679c\u5b58\u5728)\n- [ ] \u5c06\u6240\u6709\u63d0\u53d6\u7684\u4fe1\u606f\u4fdd\u5b58\u5230\u6587\u4ef6\u4e2d\n- [ ] \u5b8c\u6210\u4efb\u52a1\u5e76\u63d0\u4f9b\u7ed3\u679c",
        "append": false,
        "trailing_newline": false,
        "leading_newline": false
      }
    }
  ]
}