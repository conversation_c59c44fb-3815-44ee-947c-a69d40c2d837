import asyncio
from dotenv import load_dotenv
from browser_use.llm.google.chat import ChatGoogle

from browser_use import Agent, Controller, BrowserSession

from Policy import PolicyList  # 请自行定义模型

load_dotenv()
llm = ChatGoogle(api_key="AIzaSyAkKeMhNk41Z5b7f1aagLVrex_xXLyJ8zY")

# 任务描述
task = """
请你作为一名能够自动浏览网页并提取关键信息的 AI 浏览器助手
请你爬取以下内容：文章标题、来源网址、内容、发布机构、附件名称、附件链接
中间过程使用中文展示
"""

async def main():
    # ✅ 启动本地 Chrome：executable_path、headless 等参数由 BrowserSession 传给 Playwright
    browser_session = BrowserSession(
        executable_path="C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",  # 指定真实 Chrome
    )

    # agent.run() 会自动启动浏览器，支持 executable_path 等配置 :contentReference[oaicite:1]{index=1}
    agent = Agent(
        task=task,
        llm=llm,
        browser_session=browser_session,
        controller=Controller(output_model=PolicyList),
        initial_actions=[
            {"go_to_url": {
                "url": "https://gxj.nanjing.gov.cn/njsjjhxxhwyh/202506/t20250619_5589290.html"
            }}
        ]
    )

    # 运行 agent（内部会自动 navigates、执行交互）
    result = await agent.run()

    # ✅ 截取全页截图，确保捕获完整页面
    page = browser_session.browser_context.pages[0]
    await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
    await asyncio.sleep(2)
    await page.screenshot(path="full_page.png", full_page=True)

    print("✅ 全页截图已保存为 full_page.png")
    print("✅ 最终摘取结果如下：")
    print(result.final_result())

    await browser_session.close()

asyncio.run(main())
