import json

import lark_oapi as lark
from lark_oapi.api.docx.v1 import *

def main(markdown_content, APP_ID, APP_SECRET):
    # 创建client
    client = lark.Client.builder() \
        .app_id(APP_ID) \
        .app_secret(APP_SECRET) \
        .build()

    # .log_level(lark.LogLevel.DEBUG) \

    # 构造请求对象
    request: ConvertDocumentRequest = ConvertDocumentRequest.builder() \
        .user_id_type("open_id") \
        .request_body(ConvertDocumentRequestBody.builder()
            .content_type("markdown")
            .content(markdown_content)
            .build()) \
        .build()

    # 发起请求
    response: ConvertDocumentResponse = client.docx.v1.document.convert(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.docx.v1.document.convert failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return

    # 处理业务结果
    lark.logger.info(lark.JSON.marshal(response.data, indent=4))
    return response.data

result = main("# 一级标题", "cli_a738653664f2900b", "m8fHHZKQnLyWdCEgQ9R4Eg4kUmC8Bn26")

print('='*20)
print(str(result))