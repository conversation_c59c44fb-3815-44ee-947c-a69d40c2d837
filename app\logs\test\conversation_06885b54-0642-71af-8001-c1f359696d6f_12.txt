 system 
You are an AI agent designed to operate in an iterative loop to automate browser tasks. Your ultimate goal is accomplishing the task provided in <user_request>.

<intro>
You excel at following tasks:
1. Navigating complex websites and extracting precise information
2. Automating form submissions and interactive web actions
3. Gathering and saving information 
4. Using your filesystem effectively to decide what to keep in your context
5. Operate effectively in an agent loop
6. Efficiently performing diverse web tasks
</intro>

<language_settings>
- Default working language: **English**
- Use the language specified by user in messages as the working language
</language_settings>

<input>
At every step, your input will consist of: 
1. <agent_history>: A chronological event stream including your previous actions and their results.
2. <agent_state>: Current <user_request>, summary of <file_system>, <todo_contents>, and <step_info>.
3. <browser_state>: Current URL, open tabs, interactive elements indexed for actions, and visible page content.
4. <browser_vision>: Screenshot of the browser with bounding boxes around interactive elements.
5. <read_state> This will be displayed only if your previous action was extract_structured_data or read_file. This data is only shown in the current step.
</input>

<agent_history>
Agent history will be given as a list of step information as follows:

<step_{step_number}>:
Evaluation of Previous Step: Assessment of last action
Memory: Your memory of this step
Next Goal: Your goal for this step
Action Results: Your actions and their results
</step_{step_number}>

and system messages wrapped in <sys> tag.
</agent_history>

<user_request>
USER REQUEST: This is your ultimate objective and always remains visible.
- This has the highest priority. Make the user happy.
- If the user request is very specific - then carefully follow each step and dont skip or hallucinate steps.
- If the task is open ended you can plan yourself how to get it done.
</user_request>

<browser_state>
1. Browser State will be given as:

Current URL: URL of the page you are currently viewing.
Open Tabs: Open tabs with their indexes.
Interactive Elements: All interactive elements will be provided in format as [index]<type>text</type> where
- index: Numeric identifier for interaction
- type: HTML element type (button, input, etc.)
- text: Element description

Examples:
[33]<div>User form</div>
\t*[35]<button aria-label='Submit form'>Submit</button>

Note that:
- Only elements with numeric indexes in [] are interactive
- (stacked) indentation (with \t) is important and means that the element is a (html) child of the element above (with a lower index)
- Elements tagged with `*[` are the new clickable elements that appeared on the website since the last step - if url has not changed.
- Pure text elements without [] are not interactive.
</browser_state>

<browser_vision>
You will be optionally provided with a screenshot of the browser with bounding boxes. This is your GROUND TRUTH: reason about the image in your thinking to evaluate your progress.
Bounding box labels correspond to element indexes - analyze the image to make sure you click on correct elements.
</browser_vision>

<browser_rules>
Strictly follow these rules while using the browser and navigating the web:
- Only interact with elements that have a numeric [index] assigned.
- Only use indexes that are explicitly provided.
- If research is needed, open a **new tab** instead of reusing the current one.
- If the page changes after, for example, an input text action, analyse if you need to interact with new elements, e.g. selecting the right option from the list.
- By default, only elements in the visible viewport are listed. Use scrolling tools if you suspect relevant content is offscreen which you need to interact with. Scroll ONLY if there are more pixels below or above the page. The extract content action gets the full loaded page content.
- You can scroll by a specific number of pages using the num_pages parameter (e.g., 0.5 for half page, 2.0 for two pages).
- If a captcha appears, attempt solving it if possible. If not, use fallback strategies (e.g., alternative site, backtrack).
- If expected elements are missing, try refreshing, scrolling, or navigating back.
- If the page is not fully loaded, use the wait action.
- You can call extract_structured_data on specific pages to gather structured semantic information from the entire page, including parts not currently visible. The results of extract_structured_data are automatically saved to the file system.
- Call extract_structured_data only if the information you are looking for is not visible in your <browser_state> otherwise always just use the needed text from the <browser_state>.
- If you fill an input field and your action sequence is interrupted, most often something changed e.g. suggestions popped up under the field.
- If the <user_request> includes specific page information such as product type, rating, price, location, etc., try to apply filters to be more efficient.
- The <user_request> is the ultimate goal. If the user specifies explicit steps, they have always the highest priority.
- If you input_text into a field, you might need to press enter, click the search button, or select from dropdown for completion.
- Don't login into a page if you don't have to. Don't login if you don't have the credentials. 
- There are 2 types of tasks always first think which type of request you are dealing with:
1. Very specific step by step instructions:
- Follow them as very precise and don't skip steps. Try to complete everything as requested.
2. Open ended tasks. Plan yourself, be creative in achieving them.
- If you get stuck e.g. with logins or captcha in open-ended tasks you can re-evaluate the task and try alternative ways, e.g. sometimes accidentally login pops up, even though there some part of the page is accessible or you get some information via web search.
- If you reach a PDF viewer, the file is automatically downloaded and you can see its path in <available_file_paths>. You can either read the file or scroll in the page to see more.
</browser_rules>

<file_system>
- You have access to a persistent file system which you can use to track progress, store results, and manage long tasks.
- Your file system is initialized with a `todo.md`: Use this to keep a checklist for known subtasks. Use `replace_file_str` tool to update markers in `todo.md` as first action whenever you complete an item. This file should guide your step-by-step execution when you have a long running task.
- If you are writing a `csv` file, make sure to use double quotes if cell elements contain commas.
- If the file is too large, you are only given a preview of your file. Use `read_file` to see the full content if necessary.
- If exists, <available_file_paths> includes files you have downloaded or uploaded by the user. You can only read or upload these files but you don't have write access.
- If the task is really long, initialize a `results.md` file to accumulate your results.
- DO NOT use the file system if the task is less than 10 steps!
</file_system>

<task_completion_rules>
You must call the `done` action in one of two cases:
- When you have fully completed the USER REQUEST.
- When you reach the final allowed step (`max_steps`), even if the task is incomplete.
- If it is ABSOLUTELY IMPOSSIBLE to continue.

The `done` action is your opportunity to terminate and share your findings with the user.
- Set `success` to `true` only if the full USER REQUEST has been completed with no missing components.
- If any part of the request is missing, incomplete, or uncertain, set `success` to `false`.
- You can use the `text` field of the `done` action to communicate your findings and `files_to_display` to send file attachments to the user, e.g. `["results.md"]`.
- Combine `text` and `files_to_display` to provide a coherent reply to the user and fulfill the USER REQUEST.
- You are ONLY ALLOWED to call `done` as a single action. Don't call it together with other actions.
- If the user asks for specified format, such as "return JSON with following structure", "return a list of format...", MAKE sure to use the right format in your answer.
- If the user asks for a structured output, your `done` action's schema will be modified. Take this schema into account when solving the task!
</task_completion_rules>

<action_rules>
- You are allowed to use a maximum of 10 actions per step.

If you are allowed multiple actions:
- You can specify multiple actions in the list to be executed sequentially (one after another).
- If the page changes after an action, the sequence is interrupted and you get the new state. You can see this in your agent history when this happens.
- At every step, use ONLY ONE action to interact with the browser. DO NOT use multiple browser actions as your actions can change the browser state.

If you are allowed 1 action, ALWAYS output only the most reasonable action per step.
</action_rules>

<reasoning_rules>
You must reason explicitly and systematically at every step in your `thinking` block. 

Exhibit the following reasoning patterns to successfully achieve the <user_request>:
- Reason about <agent_history> to track progress and context toward <user_request>.
- Analyze the most recent "Next Goal" and "Action Result" in <agent_history> and clearly state what you previously tried to achieve.
- Analyze all relevant items in <agent_history>, <browser_state>, <read_state>, <file_system>, <read_state> and the screenshot to understand your state.
- Explicitly judge success/failure/uncertainty of the last action.
- If todo.md is empty and the task is multi-step, generate a stepwise plan in todo.md using file tools.
- Analyze `todo.md` to guide and track your progress. 
- If any todo.md items are finished, mark them as complete in the file.
- Analyze whether you are stuck, e.g. when you repeat the same actions multiple times without any progress. Then consider alternative approaches e.g. scrolling for more context or send_keys to interact with keys directly or different pages.
- Analyze the <read_state> where one-time information are displayed due to your previous action. Reason about whether you want to keep this information in memory and plan writing them into a file if applicable using the file tools.
- If you see information relevant to <user_request>, plan saving the information into a file.
- Before writing data into a file, analyze the <file_system> and check if the file already has some content to avoid overwriting.
- Decide what concise, actionable context should be stored in memory to inform future reasoning.
- When ready to finish, state you are preparing to call done and communicate completion/results to the user.
- Before done, use read_file to verify file contents intended for user output.
- Always reason about the <user_request>. Make sure to carefully analyze the specific steps and information required. E.g. specific filters, specific form fields, specific information to search. Make sure to always compare the current trajactory with the user request and think carefully if thats how the user requested it.
</reasoning_rules>

<examples>
Here are examples of good output patterns. Use them as reference but never copy them directly.

<todo_examples>
  "write_file": {
    "file_name": "todo.md",
    "content": "# ArXiv CS.AI Recent Papers Collection Task\n\n## Goal: Collect metadata for 20 most recent papers\n\n## Tasks:\n- [ ] Navigate to https://arxiv.org/list/cs.AI/recent\n- [ ] Initialize papers.md file for storing paper data\n- [ ] Collect paper 1/20: The Automated LLM Speedrunning Benchmark\n- [x] Collect paper 2/20: AI Model Passport\n- [ ] Collect paper 3/20: Embodied AI Agents\n- [ ] Collect paper 4/20: Conceptual Topic Aggregation\n- [ ] Collect paper 5/20: Artificial Intelligent Disobedience\n- [ ] Continue collecting remaining papers from current page\n- [ ] Navigate through subsequent pages if needed\n- [ ] Continue until 20 papers are collected\n- [ ] Verify all 20 papers have complete metadata\n- [ ] Final review and completion"
  }
</todo_examples>

<evaluation_examples>
- Positive Examples:
"evaluation_previous_goal": "Successfully navigated to the product page and found the target information. Verdict: Success"
"evaluation_previous_goal": "Clicked the login button and user authentication form appeared. Verdict: Success"
- Negative Examples:
"evaluation_previous_goal": "Failed to input text into the search bar as I cannot see it in the image. Verdict: Failure"
"evaluation_previous_goal": "Clicked the submit button with index 15 but the form was not submitted successfully. Verdict: Failure"
</evaluation_examples>

<memory_examples>
"memory": "Visited 2 of 5 target websites. Collected pricing data from Amazon ($39.99) and eBay ($42.00). Still need to check Walmart, Target, and Best Buy for the laptop comparison."
"memory": "Found many pending reports that need to be analyzed in the main page. Successfully processed the first 2 reports on quarterly sales data and moving on to inventory analysis and customer feedback reports."
</memory_examples>

<next_goal_examples>
"next_goal": "Click on the 'Add to Cart' button (index 23) to proceed with the purchase flow."
"next_goal": "Scroll down to find more product listings and extract details from the next 5 items on the page."
</next_goal_examples>
</examples>

<output>
You must ALWAYS respond with a valid JSON in this exact format:

{
  "thinking": "A structured <think>-style reasoning block that applies the <reasoning_rules> provided above.",
  "evaluation_previous_goal": "One-sentence analysis of your last action. Clearly state success, failure, or uncertain.",
  "memory": "1-3 sentences of specific memory of this step and overall progress. You should put here everything that will help you track progress in future steps. Like counting pages visited, items found, etc.",
  "next_goal": "State the next immediate goals and actions to achieve it, in one clear sentence."
  "action":[{"one_action_name": {// action-specific parameter}}, // ... more actions in sequence]
}

Action list should NEVER be empty.
</output>


 user 
<agent_history>
<sys>
Agent initialized
</sys>
<step_1>
Evaluation of Previous Step: I am at the starting page. I need to input the search query and click the search button. Verdict: Success
Memory: I need to input the search query, click the search button, sort by date, and search in the title. The policies should be from 2022-01-01 to now. I need to collect the policy title, policy link, and policy release date.
Next Goal: Input the search query '组织开展' into the search box.
Action Results:
Action 1/1: Error executing action input_text: Element index 14 does not exist - retry or use alternative actions
</step_1>
<step_2>
Evaluation of Previous Step: Failed to input text into the search bar due to an incorrect index. Verdict: Failure
Memory: I need to input the search query, click the search button, sort by date, and search in the title. The policies should be from 2022-01-01 to now. I need to collect the policy title, policy link, and policy release date. I also need to look under 政务公开.
Next Goal: Input the search query '组织开展' into the search box with index 7.
Action Results:
Action 1/1: Input '组织开展' into element 7.
</step_2>
<step_3>
Evaluation of Previous Step: Successfully inputted the text into the search bar. Verdict: Success
Memory: I need to input the search query, click the search button, sort by date, and search in the title. The policies should be from 2022-01-01 to now. I need to collect the policy title, policy link, and policy release date. I also need to look under 政务公开.
Next Goal: Click the search button with index 8.
Action Results:
Action 1/1: Clicked button with index 8: 
</step_3>
<step_4>
Evaluation of Previous Step: Successfully clicked the search button and the search results are displayed. Verdict: Success
Memory: I need to sort by date, search in the title, and only look under 政务公开. I need to collect the policy title, policy link, and policy release date from 2022-01-01 to now. I need to wait for 5 seconds after each action. I should not use the time filter.
Next Goal: Sort the search results by date by clicking the '按日期' link with index 20.
Action Results:
Action 1/2: Clicked button with index 20: 按日期
Action 2/2: Waited for 5 seconds
</step_4>
<step_5>
Evaluation of Previous Step: Successfully sorted the search results by date. Verdict: Success
Memory: I need to search in the title and only look under 政务公开. I need to collect the policy title, policy link, and policy release date from 2022-01-01 to now. I should not use the time filter. So far I have inputted the search query and sorted by date.
Next Goal: Click the '政务公开' link with index 14 to filter the search results.
Action Results:
Action 1/2: Clicked button with index 14: 政务公开
Action 2/2: Waited for 5 seconds
</step_5>
<step_6>
Evaluation of Previous Step: Successfully filtered the search results by 政务公开. Verdict: Success
Memory: I need to collect the policy title, policy link, and policy release date from 2022-01-01 to now, searching in the title and under 政务公开. So far I have inputted the search query, sorted by date, and filtered by 政务公开.
Next Goal: Click the '标题' link with index 22 to search within the title.
Action Results:
Action 1/2: Clicked button with index 22: 标题
Action 2/2: Waited for 5 seconds
</step_6>
<step_7>
Evaluation of Previous Step: Successfully filtered the search results to search only within the title. Verdict: Success
Memory: I need to collect the policy title, policy link, and policy release date from 2022-01-01 to now, searching in the title and under 政务公开. So far I have inputted the search query, sorted by date, and filtered by 政务公开 and by title.
Next Goal: Extract the policy title, link, and release date from the first result, which is '《关于组织开展“千乡万村驭风行动”的通知》政策解读' with release date '2024-04-01'.
Action Results:
Action 1/1: Scrolled down the page by 0.5 pages
</step_7>
<step_8>
Evaluation of Previous Step: Successfully scrolled down by 0.5 pages to see more search results. Verdict: Success
Memory: I need to collect the policy title, policy link, and policy release date from 2022-01-01 to now, searching in the title and under 政务公开. So far I have inputted the search query, sorted by date, and filtered by 政务公开 and by title. I have also scrolled down 0.5 pages.
Next Goal: Extract the policy title, link, and release date from the visible search results and store them in a file.
Action Results:
Action 1/1: Extracted content from https://so.ndrc.gov.cn/s?tab=zwgk&siteCode=bm04000007&qt=%E7%BB%84%E7%BB%87%E5%BC%80%E5%B1%95
<query>Extract all policy titles, policy links and policy release dates from the search results under 政务公开, with title containing '组织开展'. Filter the results with dates from 2022-01-01 to now.
</query>
<extracted_content>
Page Link: https://so.ndrc.gov.cn/s?tab=zwgk&siteCode=bm04000007&qt=%E7%BB%84%E7%BB%87%E5%BC%80%E5%B1%95
Query: Extract all policy titles, policy links and policy release dates from the search results under 政务公开, with title containing '组织开展'. Filter the results with dates from 2022-01-01 to now.
Extracted Content:
```json
{
50 more lines...
</extracted_content>
<file_system>Extracted content saved to file extracted_content_0.md successfully.</file_system>
</step_8>
<step_9>
Evaluation of Previous Step: Successfully extracted policy data from the first page. Verdict: Success
Memory: Extracted policy titles, links, and release dates from the first page of search results under 政务公开, containing '组织开展', and dated from 2022-01-01 to present. I have stored this data in 'extracted_content_0.md'. Now I need to check for more pages and extract data from them.
Next Goal: Check for a 'Next' button or page number links to navigate to the next page of search results, and if found, navigate to the next page.
Action Results:
Action 1/1: Scrolled to text: 下一页
</step_9>
<step_10>
Evaluation of Previous Step: Successfully scrolled to the '下一页' button. Verdict: Success
Memory: Extracted and saved data from the first page to 'extracted_content_0.md'. Need to extract data from subsequent pages. The search query is '组织开展', filtered by '政务公开' and '标题', sorted by date. Dates should be from 2022-01-01 to now.
Next Goal: Click the '下一页' button (index 12) to navigate to the next page and then wait for 5 seconds.
Action Results:
Action 1/2: Clicked button with index 12: 下一页
Action 2/2: Waited for 5 seconds
</step_10>
<step_11>
Evaluation of Previous Step: Successfully scrolled to the '下一页' button. Verdict: Success
Memory: Extracted and saved data from the first page to 'extracted_content_0.md'. Need to extract data from subsequent pages. The search query is '组织开展', filtered by '政务公开' and '标题', sorted by date. Dates should be from 2022-01-01 to now.
Next Goal: Click the '下一页' button (index 12) to navigate to the next page and then wait for 5 seconds.
Action Results:
Action 1/1: ('', 502)
</step_11>
</agent_history>
<agent_state>
<user_request>

你是一位信息收集专家，你要采集符合要求的以下政策数据

符合条件的政策：
1.时间：2022年1月1日-至今
2.采集的信息：政策标题，政策链接，政策发布时间

要求：
1.不要遗漏任何符合要求的政策
2.如果有多页，可以翻页
3.每次操作后等待5秒，让网页正常渲染

提示：
1.只需查找 政务公开 下的政策信息
2.排序：按日期 搜索位置：标题
3.不要使用时间筛选器

</user_request>
<file_system>
<file>
extracted_content_0.md - 55 lines
<content>
Page Link: https://so.ndrc.gov.cn/s?tab=zwgk&siteCode=bm04000007&qt=%E7%BB%84%E7%BB%87%E5%BC%80%E5%B1%95
... 47 more lines ...
      "title": "关于组织开展废旧物资循环利用体系示范城市建设的通知(发改办环资〔2022〕35号)",
      "link": "https://www.ndrc.gov.cn/xxgk/zcfb/tz/202201/t20220121_1312650.html",
      "release_date": "2022-01-21"
    }
  ]
}
```
</content>
</file>
</file_system>
<todo_contents>
[Current todo.md is empty, fill it with your plan when applicable]
</todo_contents>
<step_info>
Step 12 of 100 max possible steps
Current date and time: 2025-07-27 13:15
</step_info>
</agent_state>
<browser_state>
Current tab: 0
Available tabs:
Tab 0: https://so.ndrc.gov.cn/s?tab=zwgk&siteCode=bm04000007&qt=%E7%BB%84%E7%BB%87%E5%BC%80%E5%B1%95 - 中华人民共和国国家发展和改革委员会智能云搜索系统

Page info: 1540x788px viewport, 1525x2886px total page size, 0.0 pages above, 2.7 pages below, 3.7 total pages, at 0% of page
Interactive elements from top layer of the current page inside the viewport:
[Start of page]
[0]<a title=中华人民共和国国家发展和改革委... />
	[1]<img  />
热搜词：
[2]<a >油价 />
[3]<a >节能宣传周 />
[4]<a >能源 />
[5]<a >节能 />
[6]<a >电力 />
[7]<input type=text name=qt value=组织开展 />
[8]<input type=button value=搜索 title=搜索 />
[9]<a >无障碍模式 />
[10]<a >高级搜索 />
[11]<a >全部 />
[12]<a >机构设置 />
[13]<a >新闻动态 />
[14]<a >政务公开 />
[15]<a >政务服务 />
[16]<a >发改数据 />
[17]<a >互动交流 />
[18]<a >相关网站 />
相关结果 66条
排序：
[19]<a >按相关度 />
[20]<a >按日期 />
搜索位置：
[21]<a >全文 />
[22]<a >标题 />
[23]<div >时间不限
一周内
一月内
一年内
自定义
从
至 />
	[24]<span >时间不限 />
[25]<div >收起工具 />
政务公开
[26]<a title=国家节能中心理论学习小组组织开...>国家节能中心理论学习小组
组织
开展
强化政治机关意识主题学习研讨 />
为深入学习习近平新时代中国特色社会主义思想 强化青年干部政治机关意识 锻造坚强有力的青年干部队伍 按照 国家发展改革委关...
[27]<a >国家发展和改革委员会 />
-2020-08-26
政务公开
[28]<a title=国家节能中心党支部组织开展“不...>国家节能中心党支部
组织
开展
“不忘初心、弘扬优良家风”主题党日活动 />
为深入学习习近平新时代中国特色社会主义思想 坚定党员理想信念 按照 国家发展改革委关于
开展
强化机关意识教育的实施方案...
[29]<a >国家发展和改革委员会 />
-2020-08-20
政务公开
[30]<a title=关于组织开展绿色产业示范基地建...>关于
组织
开展
绿色产业示范基地建设的通知(发改办环资〔2020〕519号) />
国家发展改革委办公厅关于
组织
开展
绿色产业示范基地建设的通知 发改办环资 2020 519号 各省 自治区 直辖市...
[31]<a >国家发展和改革委员会 />
-2020-07-14
政务公开
[32]<a title=关于组织开展2020年（第27...>关于
组织
开展
2020年（第27批）国家企业技术中心认定工作的通知(发改办高技〔2020〕235号) />
国家发展改革委办公厅关于
组织
开展
2020年 第27批 国家企业技术中心认定工作的通知 发改办高技 2020 2...
[33]<a >国家发展和改革委员会 />
-2020-03-27
政务公开
[34]<a title=关于组织开展2019年(第26...>关于
组织
开展
2019年(第26批)国家企业技术中心认定及国家企业技术中心评价工作的通知(发改办高技〔2019〕638号) />
[35]<div  />
... 2098 pixels below (2.7 pages) - scroll to see more or extract structured data if you are looking for specific information ...
</browser_state>
<read_state>

</read_state>

Current screenshot:

 RESPONSE
{
  "thinking": "I have encountered an error after clicking the '\u4e0b\u4e00\u9875' button. The error code is 502, which indicates a server error. This suggests that the page is not loading correctly, possibly due to a temporary issue with the server or the website. I should try to reload the page and click the button again to see if the issue is resolved. If it persists, I should stop and inform the user about the issue.",
  "evaluation_previous_goal": "Failed to load the next page due to a server error (502). Verdict: Failure",
  "memory": "Extracted data from the first page to 'extracted_content_0.md'. The search query is '\u7ec4\u7ec7\u5f00\u5c55', filtered by '\u653f\u52a1\u516c\u5f00' and '\u6807\u9898', sorted by date. Dates should be from 2022-01-01 to now.",
  "next_goal": "Refresh the current page to see if the server error resolves, and if the '\u4e0b\u4e00\u9875' button works afterward. If the issue persists after refreshing, stop and inform the user.",
  "action": [
    {
      "go_back": {}
    }
  ]
}