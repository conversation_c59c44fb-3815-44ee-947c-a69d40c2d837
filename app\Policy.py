from pydantic import BaseModel, Field
from typing import List

class Attachment(BaseModel):
    name: str = Field(description="附件名称")
    url: str = Field(description="附件链接")

class Policy(BaseModel):
    title: str = Field(description="政策标题")
    url: str = Field(description="政策链接")
    fbjg: str = Field(description="发布机构")
    time: str = Field(description="发布时间")
    content: str = Field(description="政策内容")
    attachments: List[Attachment] = Field(description="附件列表")

class PolicyList(BaseModel):
    policies: List[Policy] = Field(description="政策列表")