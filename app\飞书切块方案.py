# ==============================================================================
#  集成式文本检索系统 (单文件中文版 - 零维护)
# ==============================================================================
# 本脚本将一个专业的文本分块器和一个高效的检索系统合并到一个独立的、
# 自包含的文件中。此版本【已移除】所有停用词逻辑，实现了零外部文件依赖。
#
# 它主要分为三个部分:
# 1. 分块逻辑 (CHUNKING LOGIC): 负责对文本，特别是 Markdown 格式，进行智能切分。
# 2. 检索逻辑 (RETRIEVAL LOGIC): 负责构建高效的搜索索引并执行查询。
# 3. 主程序执行 (EXECUTION): 使用示例数据演示端到端的处理流程。
#
# 必需的依赖库:
# pip install scikit-learn numpy jieba langchain_text_splitters
# ==============================================================================

import logging
import re
from typing import List, Tuple, Optional, Dict, Any

import jieba
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# --- 配置与设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入 Langchain 文本分割器以实现高级分块功能
try:
    from langchain_text_splitters import (
        RecursiveCharacterTextSplitter,
        MarkdownTextSplitter,
        MarkdownHeaderTextSplitter
    )

    LANGCHAIN_AVAILABLE = True
    logging.info("成功导入 Langchain 文本分割器。高级分块功能已启用。")
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("未找到 Langchain 文本分割器。分块功能将受限。 "
                    "请运行 'pip install langchain_text_splitters' 进行安装。")

# ==============================================================================
#  第 1 部分: 专业的文本分块逻辑 (无改动)
# ==============================================================================

# --- 用于分块的常量和正则表达式 ---
DEFAULT_CHUNK_SIZE_BYTES = 1024
DEFAULT_OVERLAP_RATIO = 0.1
MIN_CHUNK_SIZE_BYTES = 100
MAX_CHUNK_SIZE_BYTES = 8192
CODE_BLOCK_PATTERN = re.compile(r'```[\s\S]*?```')
TABLE_PATTERN = re.compile(r'\|[^\|]+\|[^\|]+\|[\s\S]*?(?:\n\n|$)')
MATH_BLOCK_PATTERN = re.compile(r'\$\$[\s\S]*?\$\$')


def get_byte_length(text: str) -> int:
    """获取字符串以 UTF-8 编码时的字节长度。"""
    return len(text.encode('utf-8'))


def extract_special_blocks(text: str) -> Tuple[str, Dict[str, str], Dict[str, str]]:
    """提取特殊块（代码、表格、数学公式）并用占位符替换它们。"""
    if not text or not text.strip():
        return text, {}, {}

    processed_text = text
    blocks = {}
    metadata = {}

    special_patterns = {
        "CODE_BLOCK": CODE_BLOCK_PATTERN,
        "TABLE_BLOCK": TABLE_PATTERN,
        "MATH_BLOCK": MATH_BLOCK_PATTERN
    }

    for block_type, pattern in special_patterns.items():
        found_blocks = pattern.findall(processed_text)
        for i, block_content in enumerate(found_blocks):
            placeholder = f"__{block_type}_{i}__"
            blocks[placeholder] = block_content
            metadata[placeholder] = {"type": block_type.lower().replace('_block', ''),
                                     "length": get_byte_length(block_content)}
            processed_text = processed_text.replace(block_content, placeholder, 1)

    return processed_text, blocks, metadata


def restore_special_blocks(text: str, blocks: Dict[str, str]) -> str:
    """从占位符恢复特殊块的原始内容。"""
    if not blocks:
        return text
    for placeholder, content in blocks.items():
        text = text.replace(placeholder, content)
    return text


class ChunkerConfig:
    """文本分块的参数配置类。"""

    def __init__(
            self,
            chunk_size_bytes: int = DEFAULT_CHUNK_SIZE_BYTES,
            overlap_ratio: float = DEFAULT_OVERLAP_RATIO,
            respect_special_blocks: bool = True,
            preserve_markdown_structure: bool = True,
            language: str = 'auto'
    ):
        if not MIN_CHUNK_SIZE_BYTES <= chunk_size_bytes <= MAX_CHUNK_SIZE_BYTES:
            logger.warning(f"分块大小 {chunk_size_bytes} 字节超出了建议范围 "
                           f"({MIN_CHUNK_SIZE_BYTES}-{MAX_CHUNK_SIZE_BYTES})。")
        self.chunk_size_bytes = chunk_size_bytes

        self.overlap_ratio = max(0.0, min(overlap_ratio, 0.5))
        self.overlap_bytes = int(self.chunk_size_bytes * self.overlap_ratio)
        self.respect_special_blocks = respect_special_blocks
        self.preserve_markdown_structure = preserve_markdown_structure
        self.language = language


class TextChunker:
    """一个能够感知语义，将文本文档切分为小片段的工具。"""

    @staticmethod
    def chunk_markdown(markdown_text: str, config: Optional[ChunkerConfig] = None) -> List[str]:
        """对 Markdown 文本进行分块，优先考虑标题、段落等语义边界。"""
        config = config or ChunkerConfig()
        if not markdown_text or not markdown_text.strip():
            logger.warning("提供的 Markdown 文本为空或只包含空白字符。")
            return []

        if not LANGCHAIN_AVAILABLE:
            logger.error("`chunk_markdown` 功能需要 Langchain。请先安装。正在回退到纯文本分块。")
            return TextChunker.chunk_text(markdown_text, config)

        processed_text, blocks, _ = extract_special_blocks(markdown_text) if config.respect_special_blocks else (
        markdown_text, {}, {})

        if get_byte_length(processed_text) <= config.chunk_size_bytes:
            return [restore_special_blocks(processed_text, blocks)]

        headers_to_split_on = [(f"#{'#' * i}", f"标题 {i + 1}") for i in range(6)]

        try:
            header_splitter = MarkdownHeaderTextSplitter(headers_to_split_on, strip_headers=False)
            header_splits_docs = header_splitter.split_text(processed_text)
            header_splits = [doc.page_content for doc in header_splits_docs if doc.page_content.strip()]
            if not header_splits: header_splits = [processed_text]
        except Exception as e:
            logger.warning(f"MarkdownHeaderTextSplitter 分割失败: {e}。将回退到递归分割。")
            header_splits = [processed_text]

        final_chunks = []
        current_chunk = ""
        for section in header_splits:
            section_size = get_byte_length(section)
            if section_size > config.chunk_size_bytes:
                if current_chunk: final_chunks.append(current_chunk)
                current_chunk = ""
                sub_chunks = TextChunker._recursive_split(section, config)
                final_chunks.extend(sub_chunks)
            elif get_byte_length(current_chunk) + section_size > config.chunk_size_bytes and current_chunk:
                final_chunks.append(current_chunk)
                current_chunk = section
            else:
                current_chunk += ("\n\n" + section) if current_chunk else section

        if current_chunk: final_chunks.append(current_chunk)

        if config.respect_special_blocks:
            final_chunks = [restore_special_blocks(chunk, blocks) for chunk in final_chunks]

        logger.info(f"Markdown 文档处理完成，共得到 {len(final_chunks)} 个最终分块。")
        return final_chunks

    @staticmethod
    def chunk_text(text: str, config: Optional[ChunkerConfig] = None) -> List[str]:
        """对纯文本进行分块，尝试尊重段落和句子边界。"""
        config = config or ChunkerConfig()
        if not text or not text.strip():
            logger.warning("提供的纯文本为空或只包含空白字符。")
            return []

        if get_byte_length(text) <= config.chunk_size_bytes:
            return [text]

        return TextChunker._recursive_split(text, config)

    @staticmethod
    def _recursive_split(text: str, config: ChunkerConfig) -> List[str]:
        """内部递归分割函数，用于纯文本或作为 Markdown 分割的回退方案。"""
        if not LANGCHAIN_AVAILABLE:
            logger.warning("Langchain 不可用。使用基础的按字节分割方法。")
            text_bytes = text.encode('utf-8')
            return [text_bytes[i:i + config.chunk_size_bytes].decode('utf-8', 'ignore')
                    for i in range(0, len(text_bytes), config.chunk_size_bytes)]

        if config.language in ('zh', 'ja', 'ko'):
            separators = ["\n\n", "\n", "。", "，", "、", ""]
        else:
            separators = ["\n\n", "\n", ". ", ", ", " ", ""]

        splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.chunk_size_bytes,
            chunk_overlap=config.overlap_bytes,
            length_function=get_byte_length,
            separators=separators
        )
        return splitter.split_text(text)


# ==============================================================================
#  第 2 部分: 高效的信息检索逻辑 (已移除停用词功能)
# ==============================================================================

class Retriever:
    """一个采用“一次构建，多次查询”模式的高效文本检索器。"""

    def __init__(self, chunks: List[str]):
        """
        初始化检索器。此版本不使用停用词。
        """
        if not chunks:
            raise ValueError("不能使用空的文本块列表来初始化检索器。")

        self.chunks = chunks

        logging.info("正在构建 TF-IDF 向量化器和索引 (无停用词)...")
        self.vectorizer = TfidfVectorizer(
            tokenizer=self._jieba_cut  # 仅指定 jieba 作为分词器
            # 此处已移除 stop_words 参数
        )
        self.chunk_vectors = self.vectorizer.fit_transform(self.chunks)
        logging.info(f"索引构建完成。向量维度: {self.chunk_vectors.shape}")

    @staticmethod
    def _jieba_cut(text: str) -> List[str]:
        """一个用于 Jieba 分词的静态方法，供向量化器调用。"""
        return list(jieba.cut(text))

    def retrieve(self, query: str, recall_num: int = 3) -> List[Tuple[float, str]]:
        """根据用户查询，在已构建的索引上执行检索。"""
        if not query.strip():
            logging.warning("查询为空。返回空结果。")
            return []

        logging.info(f"正在检索查询: '{query}'")

        query_vector = self.vectorizer.transform([query])

        similarities = cosine_similarity(query_vector, self.chunk_vectors).flatten()

        top_indices = np.argsort(similarities)[::-1][:recall_num]

        results = [(similarities[i], self.chunks[i]) for i in top_indices if similarities[i] > 0]

        logging.info(f"找到了 {len(results)} 个相关的结果。")
        return results


# ==============================================================================
#  第 3 部分: 主程序执行与功能演示
# ==============================================================================

if __name__ == "__main__":
    sample_markdown_text = """
# 自然语言处理系统报告

## 1. 系统概述

自然语言处理（NLP）是人工智能（AI）皇冠上的一颗明珠。我们的系统旨在提供一套完整的NLP解决方案，
涵盖了从文本预处理到高级语义理解的多个层面。该技术已广泛应用于机器翻译、情感分析和智能问答等场景。

## 2. 核心挑战与解决方案

尽管取得了显著进展，NLP系统在处理长文本理解、语义歧义和多语言支持方面仍面临挑战。

我们的解决方案是采用**分层注意力网络**和**多任务学习**框架。

| 特性 | 描述 |
| :--- | :--- |
| 长文本理解 | 使用层次化分块和摘要网络 |
| 语义歧义 | 结合知识图谱进行消歧 |
| 多语言支持 | 基于 mBERT 的跨语言模型 |

## 3. 模型部署

模型的部署是关键环节。我们提供云原生部署方案。

```python
# 这是一个示例代码块，将被完整保留
import torch

def deploy_model(model_path):
    # 加载模型
    model = torch.load(model_path)
    model.eval()
    print("模型已成功加载并进入评估模式！")
```
"""

    # --- 步骤 1: 使用 TextChunker 进行智能分块 ---
    print("\n" + "=" * 50)
    print("第一步: 使用 TextChunker 对文档进行智能分块")
    print("=" * 50)

    chunk_config = ChunkerConfig(
        chunk_size_bytes=500,  # 使用您提到的 500 字节分块大小
        overlap_ratio=0.1,
        language='zh'
    )

    chunks = TextChunker.chunk_markdown(sample_markdown_text, config=chunk_config)

    print(f"\n文档成功切分为 {len(chunks)} 个块。")
    print("--- 示样的第 1 个分块 ---")
    print(chunks[0])
    print("--------------------------\n")

    # --- 步骤 2: 使用 Retriever 构建搜索索引 ---
    print("=" * 50)
    print("第二步: 构建高效的检索索引 (零维护模式)")
    print("=" * 50)

    # 初始化检索器。注意：此处不再需要传入停用词路径。
    retriever = Retriever(chunks=chunks)

    # --- 步骤 3: 执行搜索查询 ---
    print("\n" + "=" * 50)
    print("第三步: 执行用户搜索查询")
    print("=" * 50)

    queries = [
        "模型部署的方案是什么？",
        "NLP面临哪些挑战？",
        "未来的研究方向"
    ]

    for query in queries:
        print(f"\n--- 查询: '{query}' ---")
        search_results = retriever.retrieve(query, recall_num=3)

        if not search_results:
            print("未能找到相关的结果。")
        else:
            for i, (score, chunk_text) in enumerate(search_results):
                print(f"\n结果 {i + 1} (相似度: {score:.4f}):")
                print("-" * 20)
                print(chunk_text)
        print("-" * 30)