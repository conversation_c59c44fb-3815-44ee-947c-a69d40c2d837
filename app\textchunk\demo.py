#!/usr/bin/env python3
"""
文本分块器演示脚本

这个脚本演示了如何使用 text_chunker.py 模块进行文本分块。
包含了各种使用场景的示例。
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from textchunk.text_chunker import TextChunker, ChunkerConfig


def demo_basic_text_chunking():
    """演示基本文本分块"""
    print("=" * 60)
    print("1. 基本文本分块演示")
    print("=" * 60)
    
    text = """
人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
语言识别、图像识别、自然语言处理和专家系统等。

机器学习是人工智能的一个重要分支。它是一种通过算法使机器能够从数据中学习并做出决策
或预测的方法。深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。

自然语言处理（Natural Language Processing，NLP）是人工智能领域中的一个重要研究方向，
它致力于让计算机能够理解、处理和生成人类语言。
"""
    
    # 使用默认配置
    config = ChunkerConfig(chunk_size_bytes=200, language='zh')
    chunks = TextChunker.chunk_text(text, config)
    
    print(f"原文本长度: {len(text)} 字符, {len(text.encode('utf-8'))} 字节")
    print(f"分块数量: {len(chunks)}")
    print()
    
    for i, chunk in enumerate(chunks, 1):
        print(f"--- 块 {i} ---")
        print(f"内容: {chunk.strip()}")
        print(f"大小: {len(chunk)} 字符, {len(chunk.encode('utf-8'))} 字节")
        print()


def demo_markdown_chunking():
    """演示 Markdown 分块"""
    print("=" * 60)
    print("2. Markdown 分块演示")
    print("=" * 60)
    
    markdown_text = """
# Python 编程指南

## 基础语法

Python 是一种高级编程语言，语法简洁明了。

### 变量定义

```python
# 基本变量类型
name = "张三"
age = 25
height = 1.75
is_student = True
```

### 控制结构

```python
# 条件语句
if age >= 18:
    print("成年人")
else:
    print("未成年人")
```

## 数据结构

Python 提供了多种内置数据结构：

| 类型 | 特点 | 示例 |
|------|------|------|
| 列表 | 有序可变 | [1, 2, 3] |
| 元组 | 有序不可变 | (1, 2, 3) |
| 字典 | 键值对 | {"key": "value"} |

## 函数定义

```python
def greet(name):
    return f"Hello, {name}!"

print(greet("World"))
```
"""
    
    config = ChunkerConfig(
        chunk_size_bytes=400,
        respect_special_blocks=True,
        preserve_markdown_structure=True
    )
    chunks = TextChunker.chunk_markdown(markdown_text, config)
    
    print(f"原 Markdown 长度: {len(markdown_text)} 字符, {len(markdown_text.encode('utf-8'))} 字节")
    print(f"分块数量: {len(chunks)}")
    print()
    
    for i, chunk in enumerate(chunks, 1):
        print(f"--- Markdown 块 {i} ---")
        print(chunk.strip())
        print(f"大小: {len(chunk)} 字符, {len(chunk.encode('utf-8'))} 字节")
        print()


def demo_custom_config():
    """演示自定义配置"""
    print("=" * 60)
    print("3. 自定义配置演示")
    print("=" * 60)
    
    text = "这是一个测试文本。" * 50  # 重复50次
    
    # 不同的配置
    configs = [
        ("小块配置", ChunkerConfig(chunk_size_bytes=100, overlap_ratio=0.1)),
        ("中块配置", ChunkerConfig(chunk_size_bytes=300, overlap_ratio=0.2)),
        ("大块配置", ChunkerConfig(chunk_size_bytes=500, overlap_ratio=0.0)),
    ]
    
    for name, config in configs:
        chunks = TextChunker.chunk_text(text, config)
        print(f"{name}:")
        print(f"  分块大小: {config.chunk_size_bytes} 字节")
        print(f"  重叠比例: {config.overlap_ratio}")
        print(f"  生成块数: {len(chunks)}")
        print(f"  平均块大小: {sum(len(c.encode('utf-8')) for c in chunks) / len(chunks):.1f} 字节")
        print()


def demo_special_content():
    """演示特殊内容处理"""
    print("=" * 60)
    print("4. 特殊内容处理演示")
    print("=" * 60)
    
    special_markdown = """
# 技术文档

## 代码示例

以下是一个完整的 Python 类定义：

```python
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

# 使用示例
calc = Calculator()
print(calc.add(5, 3))
```

## 数学公式

计算圆的面积公式：

$$A = \\pi r^2$$

其中 $r$ 是半径。

## 数据表格

| 操作 | 时间复杂度 | 空间复杂度 |
|------|-----------|-----------|
| 插入 | O(1) | O(1) |
| 查找 | O(n) | O(1) |
| 删除 | O(n) | O(1) |
"""
    
    # 测试保留特殊块 vs 不保留
    configs = [
        ("保留特殊块", ChunkerConfig(chunk_size_bytes=300, respect_special_blocks=True)),
        ("不保留特殊块", ChunkerConfig(chunk_size_bytes=300, respect_special_blocks=False)),
    ]
    
    for name, config in configs:
        chunks = TextChunker.chunk_markdown(special_markdown, config)
        print(f"{name}:")
        print(f"  分块数量: {len(chunks)}")
        
        # 检查代码块完整性
        for i, chunk in enumerate(chunks, 1):
            if '```python' in chunk:
                code_complete = chunk.count('```') % 2 == 0
                print(f"  块 {i} 包含代码，完整性: {'✓' if code_complete else '✗'}")
        print()


def main():
    """主函数"""
    print("文本分块器演示程序")
    print("作者: AI Assistant")
    print("版本: 1.0")
    print()
    
    try:
        demo_basic_text_chunking()
        demo_markdown_chunking()
        demo_custom_config()
        demo_special_content()
        
        print("=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
