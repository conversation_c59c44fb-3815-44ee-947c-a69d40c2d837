import asyncio
from dotenv import load_dotenv
from browser_use.llm.deepseek.chat import ChatDeepSeek
from browser_use import Agent, Controller, BrowserSession

from Policy import PolicyList  # ✅ 你自己定义的输出模型

load_dotenv()

# 请填入你的 DeepSeek API 密钥
deepseek_api_key = "***********************************"

# 初始化大模型
llm = ChatDeepSeek(api_key=deepseek_api_key)

# 任务说明（中文指令）
task = """
请你作为一名能够自动浏览网页并提取关键信息的 AI 浏览器助手
请你爬取以下内容：文章标题、来源网址、内容、发布机构、附件名称、附件链接
每次打开网页或进行点击等操作后等待10秒，使网页正常加载
中间过程使用中文展示
"""

# 主执行函数
async def main():
    # ✅ 正确启动本地 Chrome 浏览器（真实非模拟）
    browser_session = BrowserSession(
        executable_path="C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
        headless=False  # 可视化调试时设为 False
    )

    # 初始页面跳转（可选）
    initial_actions = [
        {
            "go_to_url": {
                "url": "https://gxj.nanjing.gov.cn/njsjjhxxhwyh/202506/t20250619_5589290.html"
            }
        }
    ]

    # 构建 Agent 并运行
    agent = Agent(
        task=task,
        llm=llm,
        browser_session=browser_session,
        controller=Controller(output_model=PolicyList),
        initial_actions=initial_actions,
    )

    result = await agent.run()

    print("✅ 最终提取结果如下：")
    print(result.final_result())

    await browser_session.close()

# 执行入口
asyncio.run(main())
