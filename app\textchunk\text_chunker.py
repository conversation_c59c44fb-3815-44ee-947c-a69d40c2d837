"""
文本分块工具，用于处理文本文档。
为不同文档类型提供优化的分块策略。
"""
import logging
import re
from typing import List, Callable, Optional, Dict, Any, Tuple, Set

# 尝试导入 langchain 文本分割器
try:
    # 首先尝试新的 langchain_text_splitters 包
    from langchain_text_splitters import (
        RecursiveCharacterTextSplitter,
        MarkdownTextSplitter,
        MarkdownHeaderTextSplitter
    )
    LANGCHAIN_AVAILABLE = True
except ImportError:
    try:
        # 回退到旧的 langchain 包结构
        from langchain.text_splitter import (
            RecursiveCharacterTextSplitter,
            MarkdownTextSplitter,
            MarkdownHeaderTextSplitter
        )
        LANGCHAIN_AVAILABLE = True
    except ImportError:
        LANGCHAIN_AVAILABLE = False
        print("未找到 Langchain 文本分割器。分块功能将受到限制。")

# 设置日志
logger = logging.getLogger(__name__)

# 默认配置值
DEFAULT_CHUNK_SIZE_BYTES = 1024  # 默认分块大小（字节）
DEFAULT_OVERLAP_RATIO = 0.1     # 默认重叠比例（相对于分块大小）
MIN_CHUNK_SIZE_BYTES = 100      # 最小允许分块大小
MAX_CHUNK_SIZE_BYTES = 8192     # 最大推荐分块大小

# 特殊内容模式
CODE_BLOCK_PATTERN = re.compile(r'```[\s\S]*?```')  # 匹配 Markdown 代码块
TABLE_PATTERN = re.compile(r'\|[^\|]+\|[^\|]+\|[\s\S]*?(?:\n\n|$)')  # 匹配 Markdown 表格
MATH_BLOCK_PATTERN = re.compile(r'\$\$[\s\S]*?\$\$')  # 匹配数学公式块


class ChunkerConfig:
    """
    文本分块参数配置类。
    """
    def __init__(
        self,
        chunk_size_bytes: int = DEFAULT_CHUNK_SIZE_BYTES,
        overlap_ratio: float = DEFAULT_OVERLAP_RATIO,
        respect_special_blocks: bool = True,
        preserve_markdown_structure: bool = True,
        language: str = 'auto'
    ):
        """
        初始化分块器配置。

        Args:
            chunk_size_bytes: 每个分块的目标大小（字节）
            overlap_ratio: 分块之间的重叠比例（0.0 到 0.5）
            respect_special_blocks: 是否保持代码块、表格等完整
            preserve_markdown_structure: 是否保留 Markdown 结构
            language: 文本语言（'auto', 'en', 'zh' 等）
        """
        # 验证并设置分块大小
        if chunk_size_bytes < MIN_CHUNK_SIZE_BYTES:
            logger.warning(f"分块大小 {chunk_size_bytes} 字节太小。使用最小值：{MIN_CHUNK_SIZE_BYTES}")
            self.chunk_size_bytes = MIN_CHUNK_SIZE_BYTES
        elif chunk_size_bytes > MAX_CHUNK_SIZE_BYTES:
            logger.warning(f"分块大小 {chunk_size_bytes} 字节很大。这可能影响性能。")
            self.chunk_size_bytes = chunk_size_bytes
        else:
            self.chunk_size_bytes = chunk_size_bytes

        # 验证并设置重叠比例
        if overlap_ratio < 0.0:
            logger.warning(f"重叠比例 {overlap_ratio} 为负数。使用 0.0。")
            self.overlap_ratio = 0.0
        elif overlap_ratio > 0.5:
            logger.warning(f"重叠比例 {overlap_ratio} 太大。使用最大值：0.5")
            self.overlap_ratio = 0.5
        else:
            self.overlap_ratio = overlap_ratio

        # 计算重叠字节数
        self.overlap_bytes = int(self.chunk_size_bytes * self.overlap_ratio)

        # 其他设置
        self.respect_special_blocks = respect_special_blocks
        self.preserve_markdown_structure = preserve_markdown_structure
        self.language = language


def get_byte_length(text: str) -> int:
    """
    获取字符串以 UTF-8 编码时的字节长度。

    Args:
        text: 要测量的文本

    Returns:
        字节长度
    """
    return len(text.encode('utf-8'))


def extract_special_blocks(text: str) -> Tuple[str, Dict[str, str], Dict[str, str]]:
    """
    从文本中提取特殊块（代码块、表格、数学公式）并用占位符替换。
    这有助于在分块过程中保留这些块。

    Args:
        text: 要处理的文本

    Returns:
        包含以下内容的元组：
        - 带有占位符的处理后文本
        - 占位符到原始内容的映射字典
        - 每个块的元数据字典
    """
    if not text or not text.strip():
        return text, {}, {}

    # 初始化
    processed_text = text
    blocks = {}
    metadata = {}

    # 处理代码块
    code_blocks = CODE_BLOCK_PATTERN.findall(text)
    for i, block in enumerate(code_blocks):
        placeholder = f"__CODE_BLOCK_{i}__"
        blocks[placeholder] = block
        metadata[placeholder] = {"type": "code", "length": get_byte_length(block)}
        processed_text = processed_text.replace(block, placeholder, 1)

    # 处理表格
    tables = TABLE_PATTERN.findall(processed_text)
    for i, block in enumerate(tables):
        placeholder = f"__TABLE_BLOCK_{i}__"
        blocks[placeholder] = block
        metadata[placeholder] = {"type": "table", "length": get_byte_length(block)}
        processed_text = processed_text.replace(block, placeholder, 1)

    # 处理数学公式块
    math_blocks = MATH_BLOCK_PATTERN.findall(processed_text)
    for i, block in enumerate(math_blocks):
        placeholder = f"__MATH_BLOCK_{i}__"
        blocks[placeholder] = block
        metadata[placeholder] = {"type": "math", "length": get_byte_length(block)}
        processed_text = processed_text.replace(block, placeholder, 1)

    return processed_text, blocks, metadata


def restore_special_blocks(text: str, blocks: Dict[str, str]) -> str:
    """
    从占位符恢复特殊块。

    Args:
        text: 带有占位符的文本
        blocks: 占位符到原始内容的映射字典

    Returns:
        恢复原始内容的文本
    """
    if not blocks:
        return text

    result = text
    for placeholder, content in blocks.items():
        result = result.replace(placeholder, content)

    return result


class TextChunker:
    """
    用于将文本文档分块为较小片段的工具类。
    支持基于文档类型的不同分块策略。
    """

    @staticmethod
    def chunk_markdown(markdown_text: str, config: Optional[ChunkerConfig] = None) -> List[str]:
        """
        分块 Markdown 文本，优先考虑语义边界（标题、段落、表格）。

        策略：
        1. 如果配置了，提取并保留特殊块（代码、表格、数学公式）
        2. 按标题分割（H1-H6）
        3. 如果不超过 max_chunk_size_bytes，合并相邻的标题块
        4. 对于仍然超过 max_chunk_size_bytes 的标题块：
           a. 尝试使用 MarkdownTextSplitter（尊重段落、列表等）
           b. 如果结果块仍然太大，使用 RecursiveCharacterTextSplitter
              作为最终回退方案处理这些特定的超大块
        5. 如果提取了特殊块，则恢复它们
        6. 执行最终大小验证

        Args:
            markdown_text: 要分块的 Markdown 文本
            config: 分块配置（如果为 None 则使用默认值）

        Returns:
            Markdown 文本块列表
        """
        # 如果未提供配置，使用默认配置
        if config is None:
            config = ChunkerConfig()

        # 为了可读性的简写
        max_chunk_size_bytes = config.chunk_size_bytes
        overlap_bytes = config.overlap_bytes
        # 处理空文本或仅包含空白字符的文本
        if not markdown_text or not markdown_text.strip():
            logger.warning("提供了空的或仅包含空白字符的 Markdown 文本。")
            return []

        # --- 1. 如果配置了，提取特殊块 ---
        blocks = {}
        blocks_metadata = {}
        processed_text = markdown_text

        if config.respect_special_blocks:
            processed_text, blocks, blocks_metadata = extract_special_blocks(markdown_text)
            logger.debug(f"从 Markdown 文本中提取了 {len(blocks)} 个特殊块")

        # --- 2. 检查 Langchain 是否可用 ---
        if not LANGCHAIN_AVAILABLE:
            logger.warning("Langchain 文本分割器不可用。回退到简单的基于大小的分割。")
            # 基本回退：纯粹按大小分割
            chunks = []
            text_bytes = processed_text.encode('utf-8')
            for i in range(0, len(text_bytes), max_chunk_size_bytes):
                chunk = text_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                # 如果提取了特殊块，则恢复它们
                if config.respect_special_blocks:
                    chunk = restore_special_blocks(chunk, blocks)
                chunks.append(chunk)
            return chunks

        # --- 3. 检查整个文档是否适合 ---
        doc_size = get_byte_length(processed_text)
        if doc_size <= max_chunk_size_bytes:
            logger.info(f"文档大小（{doc_size} 字节）在 max_chunk_size（{max_chunk_size_bytes}）范围内。作为单个块返回。")
            # 如果提取了特殊块，则恢复它们
            if config.respect_special_blocks:
                processed_text = restore_special_blocks(processed_text, blocks)
            return [processed_text]

        # --- 4. 定义要分割的标题 ---
        headers_to_split_on = [
            {"level": 1, "name": "Header 1", "tag": "#"},
            {"level": 2, "name": "Header 2", "tag": "##"},
            {"level": 3, "name": "Header 3", "tag": "###"},
            {"level": 4, "name": "Header 4", "tag": "####"},
            {"level": 5, "name": "Header 5", "tag": "#####"},
            {"level": 6, "name": "Header 6", "tag": "######"},
        ]

        # --- 5. 执行基于标题的初始分割 ---
        try:
            if not config.preserve_markdown_structure:
                # 如果不保留结构，跳过基于标题的分割
                logger.info("由于 preserve_markdown_structure=False，跳过基于标题的分割")
                raise ValueError("跳过基于标题的分割")

            header_splitter = MarkdownHeaderTextSplitter(
                headers_to_split_on=headers_to_split_on,  # 在这些标题上分割
                strip_headers=False,  # 保留标题在内容中以提供上下文
                return_each_line=False  # 处理块，而不是行
            )
            # Langchain 分割器返回 'Document' 对象。我们需要 page_content。
            header_splits_docs = header_splitter.split_text(processed_text)
            # 处理分割器可能返回没有 page_content 或空内容的对象的情况
            header_splits = [doc.page_content for doc in header_splits_docs if hasattr(doc, 'page_content') and doc.page_content]

            if not header_splits:
                # 如果标题分割没有产生任何内容（例如，没有标题），将整个文档视为一个部分
                logger.warning("MarkdownHeaderTextSplitter 没有产生任何部分。将文档视为单个部分。")
                header_splits = [processed_text]
            else:
                logger.info(f"初始标题分割创建了 {len(header_splits)} 个部分。")

        except Exception as e:
            logger.warning(f"MarkdownHeaderTextSplitter 失败：{e}。回退到整个文档的递归分割。")
            # 如果标题分割失败，回退到分割整个文档
            chunks = TextChunker._recursive_markdown_aware_split(processed_text, max_chunk_size_bytes, overlap_bytes)

            # 如果提取了特殊块，则恢复它们
            if config.respect_special_blocks:
                chunks = [restore_special_blocks(chunk, blocks) for chunk in chunks]

            return chunks

        # --- 6. 处理基于标题的分割：合并小的，分割大的 ---
        final_chunks = []          # 最终块列表
        current_chunk_content = "" # 正在构建的当前块的内容
        current_chunk_size = 0     # 当前块的大小（字节）

        for i, section_content in enumerate(header_splits):
            if not section_content.strip():  # 跳过空部分
                continue

            section_size = get_byte_length(section_content)  # 当前部分的大小

            # 情况 A：当前部分本身太大
            if section_size > max_chunk_size_bytes:
                logger.info(f"部分 {i+1}（{section_size} 字节）超过 max_chunk_size（{max_chunk_size_bytes}）。递归分割（Markdown 感知）。")
                # 如果 current_chunk_content 中有累积的内容，首先完成它
                if current_chunk_content:
                    final_chunks.append(current_chunk_content)
                    current_chunk_content = ""  # 重置当前块
                    current_chunk_size = 0

                # --- 大块的优化分割 ---
                sub_chunks = TextChunker._recursive_markdown_aware_split(section_content, max_chunk_size_bytes, overlap_bytes)
                final_chunks.extend(sub_chunks)  # 将子块添加到最终列表
                # 重置当前块内容，因为这个大块已经被处理
                current_chunk_content = ""
                current_chunk_size = 0
                continue  # 处理下一个标题部分

            # 情况 B：添加此部分会使当前块太大
            elif current_chunk_size + section_size > max_chunk_size_bytes and current_chunk_content:
                # 在变得太大之前完成当前块
                final_chunks.append(current_chunk_content)
                # 用此部分开始新块
                current_chunk_content = section_content
                current_chunk_size = section_size

            # 情况 C：部分可以添加到当前块
            else:
                # 如果已经有内容，添加换行分隔符
                if current_chunk_content:
                    current_chunk_content += "\n\n"
                    current_chunk_size += 2  # 考虑换行符
                # 将此部分添加到当前块
                current_chunk_content += section_content
                current_chunk_size += section_size

        # 不要忘记添加最后一个块（如果有内容）
        if current_chunk_content:
            final_chunks.append(current_chunk_content)

        # --- 7. 最终验证：确保没有块超过大小限制 ---
        verified_chunks = []
        for chunk in final_chunks:
            chunk_size = get_byte_length(chunk)
            if chunk_size <= max_chunk_size_bytes:
                verified_chunks.append(chunk)
            else:
                # 这在我们的算法中不应该发生，但以防万一
                logger.warning(f"处理后发现大小为 {chunk_size} 字节的块。进一步分割。")
                sub_chunks = TextChunker._recursive_markdown_aware_split(chunk, max_chunk_size_bytes, overlap_bytes)
                verified_chunks.extend(sub_chunks)

        # --- 8. 如果提取了特殊块，则恢复它们 ---
        if config.respect_special_blocks and blocks:
            verified_chunks = [restore_special_blocks(chunk, blocks) for chunk in verified_chunks]

        logger.info(f"Markdown 处理为 {len(verified_chunks)} 个最终块。")
        # 记录最小/最大/平均块大小以进行调优
        if verified_chunks:
            chunk_sizes = [get_byte_length(c) for c in verified_chunks]
            logger.info(f"块大小统计（字节）：最小={min(chunk_sizes)}，最大={max(chunk_sizes)}，平均={sum(chunk_sizes)/len(chunk_sizes):.2f}")
        return verified_chunks

    @staticmethod
    def _recursive_markdown_aware_split(text: str, max_chunk_size_bytes: int, overlap_bytes: int,
                                        language: str = 'auto') -> List[str]:
        """
        递归分割文本，优先考虑 Markdown 结构，然后回退。

        Args:
            text: 要分割的文本
            max_chunk_size_bytes: 最大块大小（字节）
            overlap_bytes: 重叠大小（字节）
            language: 文本语言，用于更好的分割

        Returns:
            文本块列表
        """
        # 处理空文本或仅包含空白字符的文本
        if not text or not text.strip():
            return []

        # 处理已经适合一个块的文本
        if get_byte_length(text) <= max_chunk_size_bytes:
            return [text]

        if not LANGCHAIN_AVAILABLE:  # 安全检查
            logger.warning("在 _recursive_markdown_aware_split 中 Langchain 分割器不可用。使用基本分割。")
            # 基本回退：纯粹按大小分割
            chunks = []
            text_bytes = text.encode('utf-8')
            for i in range(0, len(text_bytes), max_chunk_size_bytes):
                chunk = text_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                if chunk.strip():
                    chunks.append(chunk)
            return chunks

        # 确定特定语言的分隔符
        if language == 'zh' or language == 'ja' or language == 'ko':
            # 对于中文、日文、韩文：字符级分割更合适
            # 因为这些语言在单词之间不使用空格
            separators = ["\n\n", "\n", "。", "，", "、", ""]
        else:
            # 大多数语言的默认分隔符
            separators = ["\n\n", "\n", ". ", ", ", " ", ""]

        initial_chunks = []
        try:
            # --- 尝试 1：MarkdownTextSplitter ---
            # 这个分割器理解 Markdown 结构，如段落、列表、代码块。
            markdown_splitter = MarkdownTextSplitter(
                chunk_size=max_chunk_size_bytes,  # 目标大小（字节）
                chunk_overlap=overlap_bytes,      # 重叠大小（字节）
                length_function=get_byte_length   # 关键：使用字节长度
            )
            initial_chunks = markdown_splitter.split_text(text)
            logger.debug(f"尝试使用 MarkdownTextSplitter 分割，得到 {len(initial_chunks)} 个块。")

        except Exception as e:
            logger.warning(f"递归分割期间 MarkdownTextSplitter 失败：{e}。继续使用 RecursiveCharacterTextSplitter。")
            initial_chunks = [text]  # 视为一个块，由下一阶段分割

        # --- 尝试 2：RecursiveCharacterTextSplitter（回退） ---
        # 如果 MarkdownTextSplitter 失败或产生仍然太大的块，
        # 使用 RecursiveCharacterTextSplitter 作为回退。
        final_sub_chunks = []
        needs_recursive_fallback = False  # 是否需要回退的标志

        if not initial_chunks:  # 如果 MarkdownTextSplitter 没有产生任何内容
            needs_recursive_fallback = True
            initial_chunks = [text]  # 为回退分割器准备原始文本

        # 检查 MarkdownTextSplitter 的任何块是否仍然太大
        oversized_chunks = []
        valid_chunks = []
        for chunk in initial_chunks:
            if get_byte_length(chunk) > max_chunk_size_bytes:
                needs_recursive_fallback = True
                oversized_chunks.append(chunk)
            elif chunk.strip():  # 保留有效的非空块
                valid_chunks.append(chunk)

        if needs_recursive_fallback:
            logger.debug(f"对 {len(oversized_chunks)} 个超大块回退到 RecursiveCharacterTextSplitter。")
            recursive_splitter = RecursiveCharacterTextSplitter(
                chunk_size=max_chunk_size_bytes,
                chunk_overlap=overlap_bytes,
                length_function=get_byte_length,  # 使用字节长度
                separators=separators,  # 语言感知分隔符
                keep_separator=True  # 保留分隔符以更好地维护结构
            )

            # 仅处理超大块
            processed_chunks = []
            for chunk in oversized_chunks:
                split_result = recursive_splitter.split_text(chunk)
                processed_chunks.extend(split_result)

            # 将有效块与处理过的超大块合并
            final_sub_chunks = valid_chunks + processed_chunks
        else:
            # 如果 MarkdownTextSplitter 工作良好且所有块都在大小限制内
            final_sub_chunks = valid_chunks

        # 如果可能，排序块以保持原始文档顺序
        # 这假设块包含来自原始文本的一些位置信息
        # final_sub_chunks.sort(key=lambda x: text.find(x[:min(50, len(x))]) if x[:min(50, len(x))] in text else float('inf'))

        # 最后过滤掉空字符串并确保没有块超过限制
        result = []
        for chunk in final_sub_chunks:
            if not chunk.strip():
                continue

            chunk_size = get_byte_length(chunk)
            if chunk_size <= max_chunk_size_bytes:
                result.append(chunk)
            else:
                # 这是一个不应该需要的安全检查，但以防万一
                logger.warning(f"递归分割后发现大小为 {chunk_size} 字节的块。执行紧急分割。")
                # 按字节紧急分割
                chunk_bytes = chunk.encode('utf-8')
                for i in range(0, len(chunk_bytes), max_chunk_size_bytes):
                    sub_chunk = chunk_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                    if sub_chunk.strip():
                        result.append(sub_chunk)

        return result

    @staticmethod
    def chunk_text(text: str, config: Optional[ChunkerConfig] = None) -> List[str]:
        """
        分块纯文本，尝试尊重段落和句子边界。

        Args:
            text: 要分块的文本
            config: 分块配置（如果为 None 则使用默认值）

        Returns:
            文本块列表
        """
        # 如果未提供配置，使用默认配置
        if config is None:
            config = ChunkerConfig()

        # 为了可读性的简写
        max_chunk_size_bytes = config.chunk_size_bytes
        overlap_bytes = config.overlap_bytes
        language = config.language

        # 处理空文本或仅包含空白字符的文本
        if not text or not text.strip():
            logger.warning("提供了空的或仅包含空白字符的文本。")
            return []

        # 检查整个文本是否适合一个块
        if get_byte_length(text) <= max_chunk_size_bytes:
            return [text]

        # 确定特定语言的分隔符
        if language == 'zh' or language == 'ja' or language == 'ko':
            # 对于中文、日文、韩文：字符级分割更合适
            separators = ["\n\n", "\n", "。", "，", "、", ""]
        else:
            # 大多数语言的默认分隔符
            separators = ["\n\n", "\n", ". ", ", ", " ", ""]

        if not LANGCHAIN_AVAILABLE:
            # 基本回退：纯粹按大小分割
            chunks = []
            text_bytes = text.encode('utf-8')
            for i in range(0, len(text_bytes), max_chunk_size_bytes):
                chunk = text_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                if chunk.strip():
                    chunks.append(chunk)
            return chunks

        # 使用具有段落感知的 RecursiveCharacterTextSplitter
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=max_chunk_size_bytes,
            chunk_overlap=overlap_bytes,
            length_function=get_byte_length,
            separators=separators,  # 语言感知分隔符
            keep_separator=True
        )

        try:
            chunks = splitter.split_text(text)
            logger.debug(f"使用 RecursiveCharacterTextSplitter 将文本分割为 {len(chunks)} 个块")

            # 验证块
            valid_chunks = []
            for chunk in chunks:
                if not chunk.strip():
                    continue

                chunk_size = get_byte_length(chunk)
                if chunk_size <= max_chunk_size_bytes:
                    valid_chunks.append(chunk)
                else:
                    # 这在 RecursiveCharacterTextSplitter 中不应该发生，但以防万一
                    logger.warning(f"分割后发现超大块（{chunk_size} 字节）。执行紧急分割。")
                    # 按字节紧急分割
                    chunk_bytes = chunk.encode('utf-8')
                    for i in range(0, len(chunk_bytes), max_chunk_size_bytes):
                        sub_chunk = chunk_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                        if sub_chunk.strip():
                            valid_chunks.append(sub_chunk)

            return valid_chunks

        except Exception as e:
            logger.error(f"分割文本时出错：{e}。回退到基本分割。")
            # 基本回退：纯粹按大小分割
            chunks = []
            text_bytes = text.encode('utf-8')
            for i in range(0, len(text_bytes), max_chunk_size_bytes):
                chunk = text_bytes[i:i + max_chunk_size_bytes].decode('utf-8', errors='ignore')
                if chunk.strip():
                    chunks.append(chunk)
            return chunks
