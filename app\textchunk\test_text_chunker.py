"""
文本分块器测试用例

本测试文件全面测试 text_chunker.py 模块的功能，包括：
- 基本功能测试
- 边界情况测试
- 错误处理测试
- 性能测试
- 配置参数验证测试
"""

import unittest
import sys
import os
import logging
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from textchunk.text_chunker import (
    TextChunker, 
    ChunkerConfig, 
    get_byte_length,
    extract_special_blocks,
    restore_special_blocks,
    DEFAULT_CHUNK_SIZE_BYTES,
    DEFAULT_OVERLAP_RATIO,
    MIN_CHUNK_SIZE_BYTES,
    MAX_CHUNK_SIZE_BYTES
)


class TestChunkerConfig(unittest.TestCase):
    """测试 ChunkerConfig 配置类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = ChunkerConfig()
        
        self.assertEqual(config.chunk_size_bytes, DEFAULT_CHUNK_SIZE_BYTES)
        self.assertEqual(config.overlap_ratio, DEFAULT_OVERLAP_RATIO)
        self.assertEqual(config.overlap_bytes, int(DEFAULT_CHUNK_SIZE_BYTES * DEFAULT_OVERLAP_RATIO))
        self.assertTrue(config.respect_special_blocks)
        self.assertTrue(config.preserve_markdown_structure)
        self.assertEqual(config.language, 'auto')
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = ChunkerConfig(
            chunk_size_bytes=2048,
            overlap_ratio=0.2,
            respect_special_blocks=False,
            preserve_markdown_structure=False,
            language='zh'
        )
        
        self.assertEqual(config.chunk_size_bytes, 2048)
        self.assertEqual(config.overlap_ratio, 0.2)
        self.assertEqual(config.overlap_bytes, int(2048 * 0.2))
        self.assertFalse(config.respect_special_blocks)
        self.assertFalse(config.preserve_markdown_structure)
        self.assertEqual(config.language, 'zh')
    
    def test_chunk_size_validation(self):
        """测试分块大小验证"""
        # 测试过小的分块大小
        config = ChunkerConfig(chunk_size_bytes=50)
        self.assertEqual(config.chunk_size_bytes, MIN_CHUNK_SIZE_BYTES)
        
        # 测试正常的分块大小
        config = ChunkerConfig(chunk_size_bytes=1024)
        self.assertEqual(config.chunk_size_bytes, 1024)
        
        # 测试过大的分块大小（应该允许但有警告）
        with self.assertLogs(level='WARNING'):
            config = ChunkerConfig(chunk_size_bytes=10000)
            self.assertEqual(config.chunk_size_bytes, 10000)
    
    def test_overlap_ratio_validation(self):
        """测试重叠比例验证"""
        # 测试负数重叠比例
        config = ChunkerConfig(overlap_ratio=-0.1)
        self.assertEqual(config.overlap_ratio, 0.0)
        
        # 测试过大的重叠比例
        config = ChunkerConfig(overlap_ratio=0.8)
        self.assertEqual(config.overlap_ratio, 0.5)
        
        # 测试正常的重叠比例
        config = ChunkerConfig(overlap_ratio=0.3)
        self.assertEqual(config.overlap_ratio, 0.3)


class TestUtilityFunctions(unittest.TestCase):
    """测试工具函数"""
    
    def test_get_byte_length(self):
        """测试字节长度计算"""
        # 英文文本
        self.assertEqual(get_byte_length("hello"), 5)
        
        # 中文文本（UTF-8编码，每个中文字符3字节）
        self.assertEqual(get_byte_length("你好"), 6)
        
        # 混合文本
        self.assertEqual(get_byte_length("hello你好"), 11)
        
        # 空字符串
        self.assertEqual(get_byte_length(""), 0)
        
        # 特殊字符
        self.assertEqual(get_byte_length("🚀"), 4)  # emoji 4字节
    
    def test_extract_special_blocks(self):
        """测试特殊块提取"""
        text = """
# 标题

这是普通文本。

```python
def hello():
    print("Hello")
```

| 列1 | 列2 |
|-----|-----|
| 值1 | 值2 |

$$E = mc^2$$

更多文本。
"""
        
        processed_text, blocks, metadata = extract_special_blocks(text)
        
        # 检查是否提取了代码块
        code_blocks = [k for k in blocks.keys() if k.startswith('__CODE_BLOCK_')]
        self.assertEqual(len(code_blocks), 1)
        
        # 检查是否提取了表格
        table_blocks = [k for k in blocks.keys() if k.startswith('__TABLE_BLOCK_')]
        self.assertEqual(len(table_blocks), 1)
        
        # 检查是否提取了数学公式
        math_blocks = [k for k in blocks.keys() if k.startswith('__MATH_BLOCK_')]
        self.assertEqual(len(math_blocks), 1)
        
        # 检查元数据
        for placeholder, meta in metadata.items():
            self.assertIn('type', meta)
            self.assertIn('length', meta)
            self.assertIsInstance(meta['length'], int)
    
    def test_restore_special_blocks(self):
        """测试特殊块恢复"""
        original_text = "这是 ```code``` 和 $$math$$ 的测试"
        
        # 模拟提取和恢复过程
        processed_text, blocks, _ = extract_special_blocks(original_text)
        restored_text = restore_special_blocks(processed_text, blocks)
        
        self.assertEqual(restored_text, original_text)
    
    def test_extract_special_blocks_empty_text(self):
        """测试空文本的特殊块提取"""
        processed_text, blocks, metadata = extract_special_blocks("")
        self.assertEqual(processed_text, "")
        self.assertEqual(blocks, {})
        self.assertEqual(metadata, {})
        
        processed_text, blocks, metadata = extract_special_blocks("   ")
        self.assertEqual(processed_text, "   ")
        self.assertEqual(blocks, {})
        self.assertEqual(metadata, {})


class TestTextChunkerBasic(unittest.TestCase):
    """测试 TextChunker 基本功能"""
    
    def setUp(self):
        """测试前准备"""
        self.simple_text = "这是一个简单的测试文本。它包含多个句子。用于测试基本的分块功能。"
        self.markdown_text = """
# 第一章 介绍

这是第一章的内容。

## 1.1 背景

背景信息的详细描述。

## 1.2 目标

项目的目标和期望。

# 第二章 实现

第二章的具体实现内容。
"""
    
    def test_chunk_text_basic(self):
        """测试基本文本分块"""
        config = ChunkerConfig(chunk_size_bytes=50)
        chunks = TextChunker.chunk_text(self.simple_text, config)
        
        # 验证返回的是列表
        self.assertIsInstance(chunks, list)
        
        # 验证所有块都不为空
        for chunk in chunks:
            self.assertTrue(chunk.strip())
        
        # 验证块大小不超过限制
        for chunk in chunks:
            self.assertLessEqual(get_byte_length(chunk), config.chunk_size_bytes * 1.1)  # 允许小幅超出
    
    def test_chunk_markdown_basic(self):
        """测试基本 Markdown 分块"""
        config = ChunkerConfig(chunk_size_bytes=200)
        chunks = TextChunker.chunk_markdown(self.markdown_text, config)
        
        # 验证返回的是列表
        self.assertIsInstance(chunks, list)
        
        # 验证所有块都不为空
        for chunk in chunks:
            self.assertTrue(chunk.strip())
        
        # 验证至少有一个块包含标题
        has_header = any('#' in chunk for chunk in chunks)
        self.assertTrue(has_header)
    
    def test_empty_text_handling(self):
        """测试空文本处理"""
        # 测试空字符串
        chunks = TextChunker.chunk_text("")
        self.assertEqual(chunks, [])
        
        chunks = TextChunker.chunk_markdown("")
        self.assertEqual(chunks, [])
        
        # 测试只有空白字符的字符串
        chunks = TextChunker.chunk_text("   \n\t  ")
        self.assertEqual(chunks, [])
        
        chunks = TextChunker.chunk_markdown("   \n\t  ")
        self.assertEqual(chunks, [])
    
    def test_small_text_no_chunking(self):
        """测试小文本不分块"""
        small_text = "小文本"
        config = ChunkerConfig(chunk_size_bytes=1000)
        
        chunks = TextChunker.chunk_text(small_text, config)
        self.assertEqual(len(chunks), 1)
        self.assertEqual(chunks[0], small_text)
        
        chunks = TextChunker.chunk_markdown(small_text, config)
        self.assertEqual(len(chunks), 1)
        self.assertEqual(chunks[0], small_text)


class TestTextChunkerAdvanced(unittest.TestCase):
    """测试 TextChunker 高级功能"""

    def test_markdown_with_code_blocks(self):
        """测试包含代码块的 Markdown 分块"""
        markdown_with_code = """
# Python 教程

这是一个 Python 教程。

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试函数
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
```

这是代码块后的文本。

## 更多内容

继续其他内容的描述。
"""

        config = ChunkerConfig(
            chunk_size_bytes=300,
            respect_special_blocks=True
        )
        chunks = TextChunker.chunk_markdown(markdown_with_code, config)

        # 验证代码块的完整性
        code_found = False
        for chunk in chunks:
            if '```python' in chunk:
                code_found = True
                # 验证代码块是完整的
                self.assertIn('def fibonacci', chunk)
                self.assertIn('```', chunk)
                # 计算代码块的开始和结束标记
                self.assertEqual(chunk.count('```'), 2)

        self.assertTrue(code_found, "代码块应该被保留在某个分块中")

    def test_markdown_with_tables(self):
        """测试包含表格的 Markdown 分块"""
        markdown_with_table = """
# 数据对比

以下是性能对比表格：

| 算法 | 时间复杂度 | 空间复杂度 | 适用场景 |
|------|-----------|-----------|---------|
| 冒泡排序 | O(n²) | O(1) | 小数据集 |
| 快速排序 | O(n log n) | O(log n) | 一般情况 |
| 归并排序 | O(n log n) | O(n) | 稳定排序 |

表格后的说明文字。
"""

        config = ChunkerConfig(
            chunk_size_bytes=200,
            respect_special_blocks=True
        )
        chunks = TextChunker.chunk_markdown(markdown_with_table, config)

        # 验证表格的完整性
        table_found = False
        for chunk in chunks:
            if '|' in chunk and '算法' in chunk:
                table_found = True
                # 验证表格头部和至少一行数据
                self.assertIn('算法', chunk)
                self.assertIn('冒泡排序', chunk)

        self.assertTrue(table_found, "表格应该被保留在某个分块中")

    def test_chinese_text_chunking(self):
        """测试中文文本分块"""
        chinese_text = """
人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。

机器学习是人工智能的一个重要分支。它是一种通过算法使机器能够从数据中学习并做出决策或预测的方法。
深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。

自然语言处理是人工智能领域中的一个重要研究方向，它致力于让计算机能够理解、处理和生成人类语言。
计算机视觉是另一个重要的研究领域，它使计算机能够从图像和视频中提取有意义的信息。
"""

        config = ChunkerConfig(
            chunk_size_bytes=200,
            language='zh'
        )
        chunks = TextChunker.chunk_text(chinese_text, config)

        # 验证分块结果
        self.assertGreater(len(chunks), 1)

        # 验证每个块的大小
        for chunk in chunks:
            self.assertLessEqual(get_byte_length(chunk), config.chunk_size_bytes * 1.2)

        # 验证中文内容完整性
        all_text = ''.join(chunks)
        self.assertIn('人工智能', all_text)
        self.assertIn('机器学习', all_text)
        self.assertIn('自然语言处理', all_text)

    def test_overlap_functionality(self):
        """测试重叠功能"""
        text = "第一段文字。第二段文字。第三段文字。第四段文字。第五段文字。"

        config = ChunkerConfig(
            chunk_size_bytes=30,
            overlap_ratio=0.3
        )
        chunks = TextChunker.chunk_text(text, config)

        if len(chunks) > 1:
            # 检查相邻块之间是否有重叠
            for i in range(len(chunks) - 1):
                current_chunk = chunks[i]
                next_chunk = chunks[i + 1]

                # 简单检查：看是否有共同的字符序列
                # 这是一个基本的重叠检测
                overlap_found = False
                for j in range(min(10, len(current_chunk))):
                    if current_chunk[-j-1:] in next_chunk[:j+10]:
                        overlap_found = True
                        break

                # 注意：由于分割器的复杂性，重叠可能不总是字面上的
                # 这个测试主要验证配置被正确应用


class TestErrorHandling(unittest.TestCase):
    """测试错误处理和边界情况"""

    def test_none_config(self):
        """测试 None 配置处理"""
        text = "测试文本"

        # 应该使用默认配置
        chunks = TextChunker.chunk_text(text, None)
        self.assertIsInstance(chunks, list)

        chunks = TextChunker.chunk_markdown(text, None)
        self.assertIsInstance(chunks, list)

    def test_very_large_text(self):
        """测试非常大的文本"""
        # 创建一个大文本
        large_text = "这是一个测试句子。" * 1000  # 约 10KB

        config = ChunkerConfig(chunk_size_bytes=500)
        chunks = TextChunker.chunk_text(large_text, config)

        # 验证分块数量合理
        self.assertGreater(len(chunks), 10)

        # 验证每个块的大小
        for chunk in chunks:
            self.assertLessEqual(get_byte_length(chunk), config.chunk_size_bytes * 1.5)

    def test_special_characters(self):
        """测试特殊字符处理"""
        special_text = "测试🚀emoji和特殊字符：@#$%^&*()_+-=[]{}|;':\",./<>?"

        config = ChunkerConfig(chunk_size_bytes=50)
        chunks = TextChunker.chunk_text(special_text, config)

        # 验证特殊字符被正确处理
        all_text = ''.join(chunks)
        self.assertIn('🚀', all_text)
        self.assertIn('@#$%', all_text)

    @patch('textchunk.text_chunker.LANGCHAIN_AVAILABLE', False)
    def test_fallback_without_langchain(self):
        """测试没有 Langchain 时的回退机制"""
        text = "这是一个测试文本，用于验证在没有 Langchain 的情况下的回退机制。"

        config = ChunkerConfig(chunk_size_bytes=50)

        # 测试文本分块
        chunks = TextChunker.chunk_text(text, config)
        self.assertIsInstance(chunks, list)
        self.assertGreater(len(chunks), 0)

        # 测试 Markdown 分块
        chunks = TextChunker.chunk_markdown(text, config)
        self.assertIsInstance(chunks, list)
        self.assertGreater(len(chunks), 0)


class TestPerformance(unittest.TestCase):
    """性能测试"""

    def test_chunking_performance(self):
        """测试分块性能"""
        import time

        # 创建中等大小的文本
        text = "这是一个性能测试文本。" * 500  # 约 5KB

        config = ChunkerConfig(chunk_size_bytes=1024)

        # 测试文本分块性能
        start_time = time.time()
        chunks = TextChunker.chunk_text(text, config)
        text_time = time.time() - start_time

        # 测试 Markdown 分块性能
        start_time = time.time()
        chunks = TextChunker.chunk_markdown(text, config)
        markdown_time = time.time() - start_time

        # 验证性能合理（应该在1秒内完成）
        self.assertLess(text_time, 1.0)
        self.assertLess(markdown_time, 1.0)

        print(f"文本分块耗时: {text_time:.3f}秒")
        print(f"Markdown分块耗时: {markdown_time:.3f}秒")


class TestIntegration(unittest.TestCase):
    """集成测试"""

    def test_real_world_markdown_document(self):
        """测试真实世界的 Markdown 文档"""
        real_markdown = """
# Python 编程完整指南

## 目录
1. [基础语法](#基础语法)
2. [数据结构](#数据结构)
3. [函数和类](#函数和类)
4. [实际应用](#实际应用)

## 基础语法

Python 是一种高级编程语言，以其简洁和可读性而闻名。

### 变量和数据类型

```python
# 基本数据类型
name = "张三"
age = 25
height = 1.75
is_student = True
```

### 控制结构

```python
# 条件语句
if age >= 18:
    print("成年人")
else:
    print("未成年人")

# 循环
for i in range(5):
    print(f"数字: {i}")
```

## 数据结构

Python 提供了多种内置数据结构：

| 数据结构 | 特点 | 使用场景 |
|---------|------|---------|
| 列表 | 有序、可变 | 存储序列数据 |
| 元组 | 有序、不可变 | 存储固定数据 |
| 字典 | 键值对 | 快速查找 |
| 集合 | 无序、唯一 | 去重操作 |

### 列表操作

```python
# 创建和操作列表
fruits = ["苹果", "香蕉", "橙子"]
fruits.append("葡萄")
print(fruits)
```

## 函数和类

### 函数定义

```python
def calculate_area(radius):
    \"\"\"计算圆的面积\"\"\"
    import math
    return math.pi * radius ** 2

# 调用函数
area = calculate_area(5)
print(f"圆的面积: {area:.2f}")
```

### 类定义

```python
class Student:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def introduce(self):
        return f"我是{self.name}，今年{self.age}岁"

# 创建对象
student = Student("李四", 20)
print(student.introduce())
```

## 实际应用

### 文件处理

```python
# 读取文件
with open("data.txt", "r", encoding="utf-8") as file:
    content = file.read()
    print(content)

# 写入文件
with open("output.txt", "w", encoding="utf-8") as file:
    file.write("Hello, Python!")
```

### 数据分析示例

```python
import pandas as pd
import matplotlib.pyplot as plt

# 创建数据
data = {
    "姓名": ["张三", "李四", "王五"],
    "年龄": [25, 30, 35],
    "工资": [5000, 6000, 7000]
}

df = pd.DataFrame(data)
print(df)

# 绘制图表
plt.bar(df["姓名"], df["工资"])
plt.title("员工工资对比")
plt.show()
```

## 总结

Python 是一门功能强大且易学的编程语言。通过掌握基础语法、数据结构和面向对象编程，
你可以开发各种类型的应用程序。

### 学习建议

1. **多练习**：编程需要大量实践
2. **读源码**：学习优秀的开源项目
3. **参与社区**：加入 Python 开发者社区
4. **持续学习**：技术在不断发展

### 参考资源

- [Python 官方文档](https://docs.python.org/)
- [Python 教程](https://www.python.org/about/gettingstarted/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/python)

---

*本文档最后更新于 2024年*
"""

        config = ChunkerConfig(
            chunk_size_bytes=1500,
            overlap_ratio=0.1,
            respect_special_blocks=True,
            preserve_markdown_structure=True
        )

        chunks = TextChunker.chunk_markdown(real_markdown, config)

        # 验证分块结果 - 考虑到可能没有 Langchain 的情况
        # 如果没有 Langchain，可能只有一个块，但内容应该完整
        self.assertGreaterEqual(len(chunks), 1)  # 至少有一个块

        # 如果有多个块，验证分块合理性
        if len(chunks) > 1:
            self.assertGreater(len(chunks), 3)  # 应该有多个块

        # 验证重要内容被保留
        all_content = '\n'.join(chunks)
        self.assertIn('Python 编程完整指南', all_content)
        self.assertIn('def calculate_area', all_content)
        self.assertIn('class Student', all_content)

        # 验证代码块完整性 - 只在有 Langchain 且有多个块时检查
        if len(chunks) > 1:
            code_blocks_complete = True
            for chunk in chunks:
                if '```python' in chunk:
                    # 每个包含代码开始的块都应该有对应的结束
                    if chunk.count('```') % 2 != 0:
                        code_blocks_complete = False
                        break

            # 只在有多个块时严格检查代码块完整性
            if len(chunks) > 1:
                self.assertTrue(code_blocks_complete, "所有代码块应该是完整的")

        # 验证块大小 - 在没有 Langchain 时更宽松
        for chunk in chunks:
            chunk_size = get_byte_length(chunk)
            # 如果只有一个块（没有 Langchain 的情况），允许更大的大小
            if len(chunks) == 1:
                # 单个块时，只要不是空的就可以
                self.assertGreater(chunk_size, 0)
            else:
                # 多个块时，检查大小限制
                self.assertLessEqual(chunk_size, config.chunk_size_bytes * 1.5)

    def test_mixed_language_content(self):
        """测试混合语言内容"""
        mixed_content = """
# English and Chinese Mixed Content

This is an English paragraph with some technical terms.

## 中文部分

这是一个中文段落，包含一些技术术语和English words。

```python
# 代码注释可以是中文
def hello_world():
    print("Hello, 世界!")  # 混合语言字符串
    return "Success"
```

### Technical Specifications

| Feature | 中文名称 | Description |
|---------|---------|-------------|
| API | 应用程序接口 | Application Programming Interface |
| SDK | 软件开发工具包 | Software Development Kit |

## 数学公式 Mathematical Formulas

The formula for calculating area is: $$A = \\pi r^2$$

面积计算公式为：$$面积 = \\pi \\times 半径^2$$
"""

        config = ChunkerConfig(
            chunk_size_bytes=800,
            language='auto'  # 自动检测语言
        )

        chunks = TextChunker.chunk_markdown(mixed_content, config)

        # 验证混合内容被正确处理
        all_content = '\n'.join(chunks)
        self.assertIn('English and Chinese', all_content)
        self.assertIn('中文部分', all_content)
        self.assertIn('hello_world', all_content)
        self.assertIn('世界', all_content)


def run_comprehensive_tests():
    """运行全面的测试套件"""
    print("=" * 60)
    print("文本分块器 - 全面测试套件")
    print("=" * 60)

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加所有测试类
    test_classes = [
        TestChunkerConfig,
        TestUtilityFunctions,
        TestTextChunkerBasic,
        TestTextChunkerAdvanced,
        TestErrorHandling,
        TestPerformance,
        TestIntegration
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"失败数量: {len(result.failures)}")
    print(f"错误数量: {len(result.errors)}")
    print(f"跳过数量: {len(result.skipped) if hasattr(result, 'skipped') else 0}")

    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")

    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n成功率: {success_rate:.1f}%")

    return result.wasSuccessful()


if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)

    # 运行全面测试
    success = run_comprehensive_tests()

    # 退出码
    exit(0 if success else 1)
