import asyncio
from dotenv import load_dotenv
from browser_use.llm import ChatGoogle
from browser_use import Agent, Controller, BrowserSession

from Policy import PolicyList  # ✅ 你自己定义的输出模型

load_dotenv()

# 初始化大模型
llm = ChatGoogle(model="gemini-2.0-flash", api_key="AIzaSyAkKeMhNk41Z5b7f1aagLVrex_xXLyJ8zY")

# 任务说明（中文指令）
task = """
你是一位信息收集专家，你要采集符合要求的以下政策数据

符合条件的政策：
1.时间：2022年1月1日-至今
2.采集的信息：政策标题，政策链接，政策发布时间

要求：
1.不要遗漏任何符合要求的政策
2.如果有多页，可以翻页
3.每次操作后等待5秒，让网页正常渲染

提示：
1.只需查找 政务公开 下的政策信息
2.排序：按日期 搜索位置：标题
3.不要使用时间筛选器
"""

# 主执行函数
async def main():
    # ✅ 正确启动本地 Chrome 浏览器（真实非模拟）
    browser_session = BrowserSession(
        executable_path="C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
    )

    # 初始页面跳转（可选）
    initial_actions = [
        {
            "go_to_url": {
                "url": "https://so.ndrc.gov.cn/s?siteCode=bm04000007&ssl=1&token=&qt=组织开展"
            }
        }
    ]

    # 构建 Agent 并运行
    agent = Agent(
        llm=llm,
        task=task,
        browser_session=browser_session,
        initial_actions=initial_actions,
        # controller=Controller(output_model=PolicyList),
        save_conversation_path="logs/test",
        use_vision=True,
    )


    result = await agent.run()

    print("✅ 最终提取结果如下：")
    print(result.final_result())

    await browser_session.close()

# 执行入口
asyncio.run(main())
